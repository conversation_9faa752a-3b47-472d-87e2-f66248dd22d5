/// اختبارات مبسطة وعملية لتطبيق أسامة ماركت
///
/// تركز على الوظائف الأساسية والتأكد من عمل النماذج والمنطق التجاري
library simple_tests;

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:inventory_management_app/utils/formatters.dart';
import 'package:inventory_management_app/utils/validators.dart';
import 'package:inventory_management_app/config/app_colors.dart';

/// دالة مساعدة لتنظيف النصوص من الأحرف غير المرئية (RTL marks)
String cleanRTLText(String text) {
  return text
      .replaceAll('\u200F', '') // Right-to-Left Mark
      .replaceAll('\u202D', '') // Left-to-Right Override
      .replaceAll('\u202E', '') // Right-to-Left Override
      .replaceAll('\u200E', '') // Left-to-Right Mark
      .replaceAll('\u061C', '') // Arabic Letter Mark
      .trim();
}

void main() {
  // تهيئة بيانات اللغة العربية قبل تشغيل أي اختبار
  setUpAll(() async {
    await initializeDateFormatting('ar', null);
    await initializeDateFormatting('ar_SA', null);
  });

  group('اختبارات النماذج الأساسية', () {
    test('يجب أن ينشئ منتج بشكل صحيح', () {
      final Product product = Product(
        id: 1,
        name: 'منتج تجريبي',
        price: 10.0,
        barcode: '123456789',
        category: 'فئة تجريبية',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
      );

      expect(product.id, equals(1));
      expect(product.name, equals('منتج تجريبي'));
      expect(product.price, equals(10.0));
      expect(product.barcode, equals('123456789'));
      expect(product.warehouseQuantity, equals(100));
      expect(product.storeQuantity, equals(50));
      expect(product.retailPrice, equals(15.0));
    });

    test('يجب أن يحول المنتج إلى Map بشكل صحيح', () {
      final Product product = Product(
        id: 1,
        name: 'منتج تجريبي',
        price: 10.0,
        barcode: '123456789',
        category: 'فئة تجريبية',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
      );

      final Map<String, dynamic> productMap = product.toMap();

      expect(productMap['id'], equals(1));
      expect(productMap['name'], equals('منتج تجريبي'));
      expect(productMap['price'], equals(10.0));
      expect(productMap['barcode'], equals('123456789'));
      expect(productMap['warehouseQuantity'], equals(100));
      expect(productMap['storeQuantity'], equals(50));
      expect(productMap['retailPrice'], equals(15.0));
    });

    test('يجب أن ينشئ عميل بشكل صحيح', () {
      final Customer customer = Customer(
        id: 1,
        name: 'عميل تجريبي',
        phone: '0123456789',
        address: 'عنوان تجريبي',
        balance: 100.0,
        createdAt: DateTime.now(),
        notes: 'عميل مميز',
      );

      expect(customer.id, equals(1));
      expect(customer.name, equals('عميل تجريبي'));
      expect(customer.phone, equals('0123456789'));
      expect(customer.balance, equals(100.0));
      expect(customer.notes, equals('عميل مميز'));
    });

    test('يجب أن ينشئ بيع بشكل صحيح', () {
      final Sale sale = Sale(
        id: 1,
        customerId: 1,
        customerName: 'عميل تجريبي',
        total: 150.0,
        paidAmount: 100.0,
        remainingAmount: 50.0,
        saleType: 'retail',
        paymentMethod: 'cash',
        createdAt: DateTime.now(),
      );

      expect(sale.id, equals(1));
      expect(sale.customerId, equals(1));
      expect(sale.total, equals(150.0));
      expect(sale.totalAmount, equals(150.0)); // getter test
      expect(sale.paidAmount, equals(100.0));
      expect(sale.remainingAmount, equals(50.0));
      expect(sale.saleType, equals('retail'));
    });
  });

  group('اختبارات المنسقات (Formatters)', () {
    test('يجب أن ينسق العملة بشكل صحيح', () {
      // استخدام الدالة المساعدة لتنظيف النص من الأحرف غير المرئية
      String cleanResult = cleanRTLText(Formatters.formatCurrency(100.0));

      expect(cleanResult.contains('100.00'), isTrue);
      expect(cleanResult.contains('ر.س'), isTrue);

      // اختبار قيم أخرى
      cleanResult = cleanRTLText(Formatters.formatCurrency(1234.56));
      expect(cleanResult.contains('1,234.56'), isTrue);
      expect(cleanResult.contains('ر.س'), isTrue);

      // اختبار الصفر
      cleanResult = cleanRTLText(Formatters.formatCurrency(0));
      expect(cleanResult.contains('0.00'), isTrue);
      expect(cleanResult.contains('ر.س'), isTrue);
    });

    test('يجب أن ينسق الأرقام الصحيحة بشكل صحيح', () {
      expect(Formatters.formatInteger(1234), contains('1'));
      expect(Formatters.formatInteger(1234), contains('234'));
    });

    test('يجب أن ينسق الأرقام العشرية بشكل صحيح', () {
      expect(Formatters.formatDecimal(123.456, decimalPlaces: 2),
          equals('123.46'));
      expect(Formatters.formatDecimal(100.0, decimalPlaces: 0), equals('100'));
    });

    test('يجب أن ينسق النسبة المئوية بشكل صحيح', () {
      expect(Formatters.formatPercentage(25.5), equals('25.5%'));
      expect(
          Formatters.formatPercentage(100.0, decimalPlaces: 0), equals('100%'));
    });

    test('يجب أن ينسق التاريخ بشكل صحيح', () {
      final DateTime testDate = DateTime(2024, 12, 25, 14, 30);
      final String formattedDate = Formatters.formatDate(testDate);

      // التحقق من أن التاريخ يحتوي على العناصر المطلوبة
      expect(formattedDate.contains('25'), isTrue);
      expect(formattedDate.contains('12'), isTrue);
      expect(formattedDate.contains('2024'), isTrue);
    });

    test('يجب أن ينسق التاريخ والوقت بشكل صحيح', () {
      final DateTime testDateTime = DateTime(2024, 12, 25, 14, 30);
      final String formattedDateTime = Formatters.formatDateTime(testDateTime);

      // التحقق من أن النتيجة تحتوي على التاريخ والوقت
      expect(formattedDateTime.contains('25'), isTrue);
      expect(formattedDateTime.contains('12'), isTrue);
      expect(formattedDateTime.contains('2024'), isTrue);
      expect(formattedDateTime.length,
          greaterThan(10)); // يجب أن يكون أطول من التاريخ فقط
    });
  });

  group('اختبارات المدققات (Validators)', () {
    test('يجب أن يدقق البريد الإلكتروني بشكل صحيح', () {
      expect(Validators.isValidEmail('<EMAIL>'), isTrue);
      expect(Validators.isValidEmail('<EMAIL>'), isTrue);
      expect(Validators.isValidEmail('invalid-email'), isFalse);
      expect(Validators.isValidEmail(''), isFalse);
      expect(Validators.isValidEmail('test@'), isFalse);
    });

    test('يجب أن يدقق رقم الهاتف بشكل صحيح', () {
      expect(Validators.isValidPhone('0501234567'), isTrue);
      expect(Validators.isValidPhone('966501234567'), isTrue);
      expect(Validators.isValidPhone('+966501234567'), isTrue);
      expect(Validators.isValidPhone('123'), isFalse);
      expect(Validators.isValidPhone(''), isFalse);
    });

    test('يجب أن يدقق الأسعار بشكل صحيح', () {
      expect(Validators.isValidPrice('10.50'), isTrue);
      expect(Validators.isValidPrice('100'), isTrue);
      expect(Validators.isValidPrice('0'), isTrue);
      expect(Validators.isValidPrice('-10'), isFalse);
      expect(Validators.isValidPrice('abc'), isFalse);
      expect(Validators.isValidPrice(''), isFalse);
    });

    test('يجب أن يدقق الكميات بشكل صحيح', () {
      expect(Validators.isValidQuantity('10'), isTrue);
      expect(Validators.isValidQuantity('100'), isTrue);
      expect(Validators.isValidQuantity('0'), isTrue);
      expect(Validators.isValidQuantity('-5'), isFalse);
      expect(Validators.isValidQuantity('abc'), isFalse);
      expect(Validators.isValidQuantity(''), isFalse);
    });
  });

  group('اختبارات الألوان (Colors)', () {
    test('يجب أن تكون الألوان الأساسية محددة بشكل صحيح', () {
      expect(AppColors.primary, isA<Color>());
      expect(AppColors.accent, isA<Color>());
      expect(AppColors.success, isA<Color>());
      expect(AppColors.error, isA<Color>());
      expect(AppColors.warning, isA<Color>());
    });

    test('يجب أن تكون ألوان النصوص محددة بشكل صحيح', () {
      expect(AppColors.textPrimary, isA<Color>());
      expect(AppColors.textSecondary, isA<Color>());
      expect(AppColors.textOnPrimary, isA<Color>());
    });

    test('يجب أن تكون الألوان الخاصة بالتطبيق محددة بشكل صحيح', () {
      expect(AppColors.sales, isA<Color>());
      expect(AppColors.purchases, isA<Color>());
      expect(AppColors.inventory, isA<Color>());
      expect(AppColors.customers, isA<Color>());
      expect(AppColors.suppliers, isA<Color>());
      expect(AppColors.reports, isA<Color>());
    });
  });

  group('اختبارات الحسابات التجارية', () {
    test('يجب أن يحسب إجمالي البيع بشكل صحيح', () {
      const double price = 10.0;
      const int quantity = 5;
      const double discount = 5.0;

      const double total = (price * quantity) - discount;

      expect(total, equals(45.0));
    });

    test('يجب أن يحسب الربح بشكل صحيح', () {
      const double sellingPrice = 15.0;
      const double costPrice = 10.0;
      const int quantity = 10;

      const double profit = (sellingPrice - costPrice) * quantity;

      expect(profit, equals(50.0));
    });

    test('يجب أن يحسب نسبة الربح بشكل صحيح', () {
      const double sellingPrice = 15.0;
      const double costPrice = 10.0;

      const double profitPercentage =
          ((sellingPrice - costPrice) / costPrice) * 100;

      expect(profitPercentage, equals(50.0));
    });

    test('يجب أن يحسب المبلغ المتبقي بشكل صحيح', () {
      const double totalAmount = 100.0;
      const double paidAmount = 60.0;

      const double remainingAmount = totalAmount - paidAmount;

      expect(remainingAmount, equals(40.0));
    });

    test('يجب أن يحسب إجمالي مبيعات متعددة بشكل صحيح', () {
      final List<Sale> sales = <Sale>[
        Sale(
          total: 100.0,
          paidAmount: 100.0,
          remainingAmount: 0.0,
        ),
        Sale(
          total: 200.0,
          paidAmount: 150.0,
          remainingAmount: 50.0,
        ),
        Sale(
          total: 150.0,
          paidAmount: 100.0,
          remainingAmount: 50.0,
        ),
      ];

      final double totalSales =
          sales.fold(0.0, (double sum, Sale sale) => sum + (sale.total ?? 0.0));

      final double totalRemaining =
          sales.fold(0.0, (double sum, Sale sale) => sum + (sale.remainingAmount ?? 0.0));

      expect(totalSales, equals(450.0));
      expect(totalRemaining, equals(100.0));
    });
  });

  group('اختبارات copyWith للنماذج', () {
    test('يجب أن يعمل copyWith للمنتج بشكل صحيح', () {
      final Product originalProduct = Product(
        id: 1,
        name: 'منتج أصلي',
        price: 10.0,
        retailPrice: 15.0,
      );

      final Product updatedProduct = originalProduct.copyWith(
        name: 'منتج محدث',
        retailPrice: 20.0,
      );

      expect(updatedProduct.id, equals(1)); // لم يتغير
      expect(updatedProduct.name, equals('منتج محدث')); // تغير
      expect(updatedProduct.price, equals(10.0)); // لم يتغير
      expect(updatedProduct.retailPrice, equals(20.0)); // تغير
    });
  });
}
