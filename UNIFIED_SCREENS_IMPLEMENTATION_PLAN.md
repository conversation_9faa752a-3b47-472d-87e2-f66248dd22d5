# خطة توحيد شاشات إدارة المنتجات والمبيعات والمشتريات

## 📋 **تحليل الوضع الحالي**

### **🔍 الشاشات المكررة المكتشفة:**

#### **1. شاشات المنتجات:**
- ✅ **تم حلها مسبقاً**: `enhanced_product_form_screen.dart` (موحدة)
- ❌ **محذوفة**: `add_edit_product_screen.dart`, `product_form_screen.dart`

#### **2. شاشات فواتير البيع:**
- ✅ **موجودة**: `create_sale_invoice_screen.dart` (تدعم الإضافة والتعديل)
- ❌ **محذوفة**: `sale_invoice_form_screen.dart`

#### **3. شاشات فواتير الشراء:**
- ⚠️ **تحتاج تطوير**: `create_purchase_invoice_screen.dart` (غير مكتملة)

---

## 🎯 **الهدف من التوحيد**

### **المبادئ الأساسية:**
1. **شاشة واحدة لكل نوع** مع وسائط (arguments) لتحديد الوضع
2. **وضع الإضافة**: `mode: 'add'` أو `existingItem: null`
3. **وضع التعديل**: `mode: 'edit'` أو `existingItem: item`
4. **واجهة موحدة** لجميع العمليات المتشابهة

---

## 🔧 **التحسينات المطلوبة**

### **1. إكمال شاشة فواتير الشراء**

#### **المشكلة الحالية:**
```dart
// lib/screens/invoices/create_purchase_invoice_screen.dart
class CreatePurchaseInvoiceScreen extends StatefulWidget {
  final Purchase? existingPurchase; // ✅ يدعم التعديل
  
  // ❌ لكن التنفيذ غير مكتمل
  // TODO: تحميل PurchaseItems من قاعدة البيانات
  // TODO: ربط مع PurchaseProvider
}
```

#### **الحل المطلوب:**
- إكمال تنفيذ شاشة إنشاء/تعديل فواتير الشراء
- ربطها مع `PurchaseProvider`
- إضافة دعم كامل لعناصر الفاتورة

### **2. توحيد نمط التصميم**

#### **النمط الموحد المقترح:**
```dart
class UnifiedFormScreen<T> extends StatefulWidget {
  final T? existingItem;           // العنصر للتعديل (null للإضافة)
  final String formType;           // نوع النموذج ('product', 'sale', 'purchase')
  final String title;              // عنوان الشاشة
  final Function(T) onSave;        // دالة الحفظ
  final Function(T)? onDelete;     // دالة الحذف (اختيارية)
  
  const UnifiedFormScreen({
    super.key,
    this.existingItem,
    required this.formType,
    required this.title,
    required this.onSave,
    this.onDelete,
  });
}
```

---

## 📁 **الملفات المطلوب تحديثها**

### **1. إكمال شاشة فواتير الشراء:**
- `lib/screens/invoices/create_purchase_invoice_screen.dart`
- `lib/providers/purchase_provider.dart`
- `lib/core/app_router.dart`

### **2. تحديث المراجع:**
- `lib/screens/invoices/purchase_invoice_list_screen.dart`
- `lib/screens/dashboard/dashboard_screen.dart`

### **3. إضافة الحوارات المفقودة:**
- `lib/screens/dialogs/select_supplier_dialog.dart`
- `lib/screens/dialogs/add_product_to_purchase_dialog.dart`

---

## 🔄 **خطة التنفيذ**

### **المرحلة 1: إكمال فواتير الشراء**
1. ✅ إنشاء `select_supplier_dialog.dart`
2. ✅ إنشاء `add_product_to_purchase_dialog.dart`
3. ✅ إكمال `create_purchase_invoice_screen.dart`
4. ✅ تحديث `purchase_provider.dart`
5. ✅ اختبار التكامل

### **المرحلة 2: توحيد الأنماط**
1. ✅ توحيد تصميم AppBar
2. ✅ توحيد أزرار الحفظ والإلغاء
3. ✅ توحيد معالجة الأخطاء
4. ✅ توحيد رسائل النجاح

### **المرحلة 3: التحسينات الإضافية**
1. ✅ إضافة validation موحد
2. ✅ إضافة auto-save
3. ✅ تحسين UX/UI
4. ✅ إضافة shortcuts

---

## 🎨 **التصميم الموحد**

### **1. AppBar موحد:**
```dart
AppBar _buildUnifiedAppBar() {
  return AppBar(
    title: Text(widget.existingItem != null 
        ? 'تعديل ${widget.title}' 
        : 'إضافة ${widget.title}'),
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnPrimary,
    actions: [
      if (widget.existingItem != null && widget.onDelete != null)
        IconButton(
          icon: const Icon(Icons.delete),
          onPressed: _confirmDelete,
        ),
      IconButton(
        icon: const Icon(Icons.save),
        onPressed: _saveItem,
      ),
    ],
  );
}
```

### **2. أزرار سفلية موحدة:**
```dart
Widget _buildUnifiedBottomActions() {
  return Container(
    padding: const EdgeInsets.all(AppDimensions.paddingM),
    child: SafeArea(
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _handleCancel,
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _saveItem,
              icon: Icon(widget.existingItem != null ? Icons.save : Icons.add),
              label: Text(widget.existingItem != null 
                  ? 'حفظ التغييرات' 
                  : 'إضافة ${widget.title}'),
            ),
          ),
        ],
      ),
    ),
  );
}
```

---

## ✅ **الفوائد المتوقعة**

### **1. تقليل التكرار:**
- ❌ **قبل**: 3 ملفات لكل نوع (إضافة، تعديل، عرض)
- ✅ **بعد**: ملف واحد لكل نوع يدعم جميع العمليات

### **2. سهولة الصيانة:**
- تحديث واحد يؤثر على جميع الشاشات المتشابهة
- أقل احتمالية للأخطاء
- كود أكثر تنظيماً

### **3. تجربة مستخدم موحدة:**
- نفس التصميم والسلوك في جميع الشاشات
- تعلم أسرع للمستخدمين
- واجهة أكثر احترافية

### **4. تطوير أسرع:**
- إضافة ميزات جديدة أسهل
- اختبار أقل تعقيداً
- نشر أسرع للتحديثات

---

## 🚀 **الخطوات التالية**

1. **تنفيذ المرحلة 1**: إكمال فواتير الشراء
2. **اختبار شامل**: للتأكد من عمل جميع الوظائف
3. **تطبيق المرحلة 2**: توحيد الأنماط
4. **مراجعة الجودة**: code review شامل
5. **التوثيق**: تحديث الدليل للمطورين

---

**الحالة**: 🟡 قيد التنفيذ  
**الأولوية**: 🔴 عالية  
**المدة المتوقعة**: 2-3 أيام عمل
