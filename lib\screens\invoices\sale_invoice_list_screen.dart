import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/sale_provider.dart';
import '../../providers/customer_provider.dart';
import '../../models/sale.dart';
import '../../models/customer.dart';
import '../dialogs/confirmation_dialog.dart';
import 'create_sale_invoice_screen.dart';
import 'sale_invoice_details_screen.dart';

class SaleInvoiceListScreen extends StatefulWidget {
  const SaleInvoiceListScreen({super.key});

  @override
  State<SaleInvoiceListScreen> createState() => _SaleInvoiceListScreenState();
}

class _SaleInvoiceListScreenState extends State<SaleInvoiceListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatus = 'الكل';
  String _selectedPeriod = 'الكل';
  List<Sale> _filteredSales = <Sale>[];
  bool _isLoading = true;

  final List<String> _statusOptions = <String>[
    'الكل',
    'مدفوعة',
    'غير مدفوعة',
    'ملغاة'
  ];
  final List<String> _periodOptions = <String>[
    'الكل',
    'اليوم',
    'الأسبوع',
    'الشهر',
    'السنة',
    'تحديد يدوي'
  ];

  @override
  void initState() {
    super.initState();
    _loadSales();
  }

  Future<void> _loadSales() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final SaleProvider saleProvider = context.read<SaleProvider>();
      await saleProvider.fetchSales();
      _filterSales();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في تحميل الفواتير: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterSales() {
    final SaleProvider saleProvider = context.read<SaleProvider>();
    List<Sale> sales = saleProvider.sales;

    // تصفية حسب النص
    if (_searchController.text.isNotEmpty) {
      final String query = _searchController.text.toLowerCase();
      sales = sales.where((Sale sale) {
        final String customerName =
            _getCustomerName(sale.customerId).toLowerCase();
        return sale.id.toString().contains(query) ||
            customerName.contains(query);
      }).toList();
    }

    // تصفية حسب الحالة
    if (_selectedStatus != 'الكل') {
      sales = sales.where((Sale sale) {
        switch (_selectedStatus) {
          case 'مدفوعة':
            return sale.status == 'completed';
          case 'غير مدفوعة':
            return sale.status != 'completed';
          case 'ملغاة':
            return sale.status == 'cancelled';
          default:
            return true;
        }
      }).toList();
    }

    // تصفية حسب الفترة
    if (_selectedPeriod != 'الكل') {
      final DateTime now = DateTime.now();
      sales = sales.where((Sale sale) {
        if (sale.date == null) return false;
        final DateTime saleDate = DateTime.parse(sale.date!);

        switch (_selectedPeriod) {
          case 'اليوم':
            return saleDate.year == now.year &&
                saleDate.month == now.month &&
                saleDate.day == now.day;
          case 'الأسبوع':
            final DateTime weekStart =
                now.subtract(Duration(days: now.weekday - 1));
            return saleDate
                .isAfter(weekStart.subtract(const Duration(days: 1)));
          case 'الشهر':
            return saleDate.year == now.year && saleDate.month == now.month;
          case 'السنة':
            return saleDate.year == now.year;
          default:
            return true;
        }
      }).toList();
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    sales.sort((Sale a, Sale b) {
      if (a.date == null && b.date == null) return 0;
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return DateTime.parse(b.date!).compareTo(DateTime.parse(a.date!));
    });

    setState(() {
      _filteredSales = sales;
    });
  }

  String _getCustomerName(int? customerId) {
    if (customerId == null) return 'عميل غير محدد';
    final CustomerProvider customerProvider = context.read<CustomerProvider>();
    final Customer? customer = customerProvider.customers
        .where((Customer c) => c.id == customerId)
        .firstOrNull;
    return customer?.name ?? 'عميل غير محدد';
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final DateTime date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  String _getStatusText(String? status) {
    switch (status) {
      case 'completed':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return 'غير مدفوعة';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('فواتير البيع'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.add_circle),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        const CreateSaleInvoiceScreen(),
                  ),
                ).then((_) => _loadSales());
              },
            ),
          ],
        ),
        body: Column(
          children: <Widget>[
            // شريط البحث والتصفية
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: <BoxShadow>[
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Column(
                children: <Widget>[
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'البحث',
                      hintText: 'رقم الفاتورة أو اسم العميل',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (String value) => _filterSales(),
                  ),

                  const SizedBox(height: 12),

                  // خيارات التصفية
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedStatus,
                          decoration: const InputDecoration(
                            labelText: 'الحالة',
                            border: OutlineInputBorder(),
                          ),
                          items: _statusOptions.map((String status) {
                            return DropdownMenuItem(
                              value: status,
                              child: Text(status),
                            );
                          }).toList(),
                          onChanged: (String? value) {
                            setState(() {
                              _selectedStatus = value!;
                            });
                            _filterSales();
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedPeriod,
                          decoration: const InputDecoration(
                            labelText: 'الفترة',
                            border: OutlineInputBorder(),
                          ),
                          items: _periodOptions.map((String period) {
                            return DropdownMenuItem(
                              value: period,
                              child: Text(period),
                            );
                          }).toList(),
                          onChanged: (String? value) {
                            setState(() {
                              _selectedPeriod = value!;
                            });
                            _filterSales();
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // قائمة الفواتير
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredSales.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Icon(Icons.receipt_long,
                                  size: 64, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'لا توجد فواتير',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: _loadSales,
                          child: ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _filteredSales.length,
                            itemBuilder: (BuildContext context, int index) {
                              final Sale sale = _filteredSales[index];
                              return _buildSaleItem(sale);
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaleItem(Sale sale) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // الصف الأول: رقم الفاتورة والحالة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'فاتورة رقم ${sale.id}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(sale.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: _getStatusColor(sale.status)),
                  ),
                  child: Text(
                    _getStatusText(sale.status),
                    style: TextStyle(
                      color: _getStatusColor(sale.status),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // الصف الثاني: العميل والتاريخ
            Row(
              children: <Widget>[
                const Icon(Icons.person, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    _getCustomerName(sale.customerId),
                    style: const TextStyle(color: Colors.grey),
                  ),
                ),
                const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  _formatDate(sale.date),
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // الصف الثالث: المبلغ والأزرار
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  '${sale.totalAmount?.toStringAsFixed(2) ?? '0.00'} ر.س',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    IconButton(
                      onPressed: () => _viewSaleDetails(sale),
                      icon: const Icon(Icons.visibility, color: Colors.blue),
                      tooltip: 'عرض التفاصيل',
                    ),
                    IconButton(
                      onPressed: () => _editSale(sale),
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      tooltip: 'تعديل',
                    ),
                    IconButton(
                      onPressed: () => _deleteSale(sale),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      tooltip: 'حذف',
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _viewSaleDetails(Sale sale) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => SaleInvoiceDetailsScreen(sale: sale),
      ),
    ).then((_) => _loadSales());
  }

  Future<void> _editSale(Sale sale) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => EditConfirmationDialog(
        title: 'تأكيد التعديل',
        content: 'هل أنت متأكد من رغبتك في تعديل هذه الفاتورة؟',
        itemName: 'فاتورة رقم ${sale.id}',
      ),
    );

    if (confirmed == true && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (BuildContext context) =>
              CreateSaleInvoiceScreen(existingSale: sale),
        ),
      ).then((_) => _loadSales());
    }
  }

  Future<void> _deleteSale(Sale sale) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => DeleteConfirmationDialog(
        title: 'تأكيد الحذف',
        content: 'هل أنت متأكد من حذف هذه الفاتورة؟',
        itemName: 'فاتورة رقم ${sale.id}',
      ),
    );

    if (confirmed == true) {
      try {
        final SaleProvider saleProvider = context.read<SaleProvider>();
        await saleProvider.deleteSale(sale.id!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الفاتورة بنجاح')),
          );
          _loadSales();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ في حذف الفاتورة: $e')),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
