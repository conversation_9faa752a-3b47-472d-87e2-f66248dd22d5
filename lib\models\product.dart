/// نموذج المنتج مع final fields لضمان immutability
class Product {
  final int? id;
  final String name;
  final String description;
  final double price; // Wholesale price
  final int quantity; // كمية موحدة (تحويل من double إلى int)
  final int? categoryId;
  final int? unitId;
  final int? supplierId;

  // خصائص إضافية مطلوبة
  final String? category;
  final String? unit;
  final double? purchasePrice;
  final double? salePrice;
  final int minLevel; // مستوى الحد الأدنى (مطلوب)
  final String? barcode;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? date;

  // حقول جديدة لنظام المخزن والبقالة
  final double? retailPrice; // سعر البيع بالتجزئة
  final double? wholesalePrice; // سعر البيع بالجملة
  final int warehouseQuantity; // الكمية المتوفرة في المخزن (مطلوب)
  final int storeQuantity; // الكمية المتوفرة في البقالة (مطلوب)

  Product({
    this.id,
    required this.name,
    this.description = '',
    required this.price,
    this.quantity = 0,
    this.categoryId,
    this.unitId,
    this.supplierId,
    this.category,
    this.unit,
    this.purchasePrice,
    this.salePrice,
    this.minLevel = 0,
    this.barcode,
    this.createdAt,
    this.updatedAt,
    this.date,
    this.retailPrice,
    this.wholesalePrice,
    this.warehouseQuantity = 0,
    this.storeQuantity = 0,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'quantity': quantity,
      'categoryId': categoryId,
      'unitId': unitId,
      'supplierId': supplierId,
      'category': category,
      'unit': unit,
      'purchasePrice': purchasePrice,
      'salePrice': salePrice,
      'minLevel': minLevel,
      'barcode': barcode,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'date': date,
      'retailPrice': retailPrice,
      'wholesalePrice': wholesalePrice,
      'warehouseQuantity': warehouseQuantity,
      'storeQuantity': storeQuantity,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      quantity: _parseQuantity(map['quantity']),
      categoryId: map['categoryId'],
      unitId: map['unitId'],
      supplierId: map['supplierId'],
      category: map['category'],
      unit: map['unit'],
      purchasePrice: map['purchasePrice']?.toDouble(),
      salePrice: map['salePrice']?.toDouble(),
      minLevel: _parseQuantity(map['minLevel']),
      barcode: map['barcode'],
      createdAt:
          map['createdAt'] != null ? DateTime.parse(map['createdAt']) : null,
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
      date: map['date'],
      retailPrice: map['retailPrice']?.toDouble(),
      wholesalePrice: map['wholesalePrice']?.toDouble(),
      warehouseQuantity: _parseQuantity(map['warehouseQuantity']),
      storeQuantity: _parseQuantity(map['storeQuantity']),
    );
  }

  /// دالة مساعدة لتحويل القيم إلى int بأمان
  static int _parseQuantity(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  /// إنشاء نسخة جديدة من المنتج مع تحديث بعض الخصائص
  Product copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    int? quantity,
    int? categoryId,
    int? unitId,
    int? supplierId,
    String? category,
    String? unit,
    double? purchasePrice,
    double? salePrice,
    int? minLevel,
    String? barcode,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? date,
    double? retailPrice,
    double? wholesalePrice,
    int? warehouseQuantity,
    int? storeQuantity,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      categoryId: categoryId ?? this.categoryId,
      unitId: unitId ?? this.unitId,
      supplierId: supplierId ?? this.supplierId,
      category: category ?? this.category,
      unit: unit ?? this.unit,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      salePrice: salePrice ?? this.salePrice,
      minLevel: minLevel ?? this.minLevel,
      barcode: barcode ?? this.barcode,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      date: date ?? this.date,
      retailPrice: retailPrice ?? this.retailPrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      warehouseQuantity: warehouseQuantity ?? this.warehouseQuantity,
      storeQuantity: storeQuantity ?? this.storeQuantity,
    );
  }
}
