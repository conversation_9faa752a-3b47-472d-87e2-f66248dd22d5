import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/purchase.dart';
import '../../models/purchase_item.dart';
import '../../models/supplier.dart';
import '../../providers/purchase_provider.dart';
import '../../providers/supplier_provider.dart';
import '../../config/app_colors.dart';
import '../../config/app_dimensions.dart';
import '../../config/app_styles.dart';
import '../../utils/formatters.dart';
import '../../utils/validators.dart';
import '../../widgets/enhanced_confirmation_dialog.dart';
import '../dialogs/select_supplier_dialog.dart';
import '../dialogs/add_product_to_purchase_dialog.dart';

class CreatePurchaseInvoiceScreen extends StatefulWidget {
  final Purchase? existingPurchase; // للتعديل

  const CreatePurchaseInvoiceScreen({
    super.key,
    this.existingPurchase,
  });

  @override
  State<CreatePurchaseInvoiceScreen> createState() =>
      _CreatePurchaseInvoiceScreenState();
}

class _CreatePurchaseInvoiceScreenState
    extends State<CreatePurchaseInvoiceScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _invoiceNumberController =
      TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _supplierController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  Supplier? _selectedSupplier;
  DateTime _selectedDate = DateTime.now();
  final List<PurchaseItem> _purchaseItems = <PurchaseItem>[];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingPurchase != null) {
      // تحميل بيانات الفاتورة الموجودة للتعديل
      final Purchase purchase = widget.existingPurchase!;
      _invoiceNumberController.text = purchase.id?.toString() ?? '';
      _selectedDate =
          DateTime.parse(purchase.date ?? DateTime.now().toIso8601String());
      _dateController.text = _formatDate(_selectedDate);
      _notesController.text = purchase.notes ?? '';

      // تحميل المورد
      if (purchase.supplierId != null) {
        // TODO: تحميل بيانات المورد من قاعدة البيانات
        _supplierController.text = 'مورد رقم ${purchase.supplierId}';
      }

      // تحميل عناصر الفاتورة
      // TODO: تحميل PurchaseItems من قاعدة البيانات
    } else {
      // إنشاء فاتورة جديدة
      _invoiceNumberController.text = _generateInvoiceNumber();
      _dateController.text = _formatDate(_selectedDate);
    }
  }

  String _generateInvoiceNumber() {
    final DateTime now = DateTime.now();
    return 'PUR-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch.toString().substring(8)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  double get _totalAmount {
    return _purchaseItems.fold(
        0.0, (double sum, PurchaseItem item) => sum + (item.totalPrice ?? 0));
  }

  void _calculateTotal() {
    // تحديث المجموع الإجمالي
    setState(() {
      // المجموع يتم حسابه تلقائياً من خلال getter
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(widget.existingPurchase != null
              ? 'تعديل فاتورة توريد'
              : 'فاتورة توريد جديدة'),
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _savePurchaseInvoice,
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: Column(
            children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // رقم الفاتورة
                      TextFormField(
                        controller: _invoiceNumberController,
                        decoration: const InputDecoration(
                          labelText: 'رقم فاتورة التوريد',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.receipt_long),
                        ),
                        readOnly: true,
                      ),

                      const SizedBox(height: 16),

                      // التاريخ
                      TextFormField(
                        controller: _dateController,
                        decoration: const InputDecoration(
                          labelText: 'التاريخ',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: _selectDate,
                      ),

                      const SizedBox(height: 16),

                      // المورد
                      TextFormField(
                        controller: _supplierController,
                        decoration: const InputDecoration(
                          labelText: 'المورد',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.business),
                          suffixIcon: Icon(Icons.search),
                        ),
                        readOnly: true,
                        onTap: _selectSupplier,
                        validator: (String? value) {
                          if (_selectedSupplier == null) {
                            return 'يرجى اختيار المورد';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 24),

                      // عنوان المنتجات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          const Text(
                            'المنتجات',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _addProduct,
                            icon: const Icon(Icons.add),
                            label: const Text('إضافة منتج'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // قائمة المنتجات
                      _buildProductsList(),

                      const SizedBox(height: 24),

                      // المبلغ الإجمالي
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Text(
                          'المبلغ الإجمالي: ${_totalAmount.toStringAsFixed(2)} ر.س',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // الملاحظات
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'ملاحظات (اختياري)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.note),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              // شريط الأزرار السفلي
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _savePurchaseInvoice,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            widget.existingPurchase != null
                                ? 'حفظ التعديلات'
                                : 'حفظ فاتورة التوريد',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    if (_purchaseItems.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Column(
          children: <Widget>[
            Icon(Icons.inventory_2_outlined, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              'لم يتم إضافة أي منتجات بعد',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _purchaseItems.length,
      itemBuilder: (BuildContext context, int index) {
        final PurchaseItem item = _purchaseItems[index];
        return _buildProductItem(item, index);
      },
    );
  }

  Widget _buildProductItem(PurchaseItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    item.productName ?? 'منتج غير محدد',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'الكمية: ${item.quantity?.toStringAsFixed(0) ?? '0'}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                  Text(
                    'السعر: ${item.unitPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
            Column(
              children: <Widget>[
                Text(
                  '${item.totalPrice?.toStringAsFixed(2) ?? '0.00'} ر.س',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    IconButton(
                      onPressed: () => _editProductQuantity(index),
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      iconSize: 20,
                    ),
                    IconButton(
                      onPressed: () => _removeProduct(index),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      iconSize: 20,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = _formatDate(picked);
      });
    }
  }

  Future<void> _selectSupplier() async {
    await showDialog<Supplier>(
      context: context,
      builder: (BuildContext context) => SelectSupplierDialog(
        selectedSupplier: _selectedSupplier,
        onSupplierSelected: (Supplier supplier) {
          setState(() {
            _selectedSupplier = supplier;
            _supplierController.text = supplier.name ?? '';
          });
        },
      ),
    );
  }

  Future<void> _addProduct() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) => AddProductToPurchaseDialog(
        onProductAdded: (PurchaseItem item) {
          setState(() {
            _purchaseItems.add(item);
            _calculateTotal();
          });
        },
      ),
    );
  }

  void _editProductQuantity(int index) {
    final PurchaseItem item = _purchaseItems[index];
    final TextEditingController quantityController = TextEditingController(
      text: item.quantity?.toString() ?? '1',
    );

    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تعديل الكمية'),
        content: TextFormField(
          controller: quantityController,
          decoration: const InputDecoration(
            labelText: 'الكمية',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              final double? newQuantity =
                  double.tryParse(quantityController.text);
              if (newQuantity != null && newQuantity > 0) {
                setState(() {
                  _purchaseItems[index] = _purchaseItems[index].copyWith(
                    quantity: newQuantity,
                  );
                });
              }
              Navigator.pop(context);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _removeProduct(int index) {
    setState(() {
      _purchaseItems.removeAt(index);
    });
  }

  Future<void> _savePurchaseInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_purchaseItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة منتج واحد على الأقل')),
      );
      return;
    }

    // عرض تأكيد
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(
            widget.existingPurchase != null ? 'تأكيد التعديل' : 'تأكيد الحفظ'),
        content: Text(widget.existingPurchase != null
            ? 'هل أنت متأكد من تعديل فاتورة التوريد هذه؟ قد يؤثر ذلك على المخزون والسجلات المالية.'
            : 'هل أنت متأكد من إتمام فاتورة التوريد هذه؟'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // حفظ فاتورة التوريد
      await Future.delayed(const Duration(seconds: 2)); // محاكاة العملية

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ فاتورة التوريد بنجاح')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _dateController.dispose();
    _supplierController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
