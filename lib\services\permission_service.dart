/// خدمة الصلاحيات الموحدة لتطبيق أسامة ماركت
/// 
/// توفر طرق موحدة لطلب وفحص جميع الصلاحيات المطلوبة في التطبيق
/// 
/// الصلاحيات المدعومة:
/// - التخزين (Storage)
/// - الكاميرا (Camera)
/// - الموقع (Location)
/// - جهات الاتصال (Contacts)
/// - الإشعارات (Notifications)
/// - الميكروفون (Microphone)
/// 
/// الاستخدام:
/// ```dart
/// final bool hasPermission = await PermissionService.requestStoragePermission();
/// if (hasPermission) {
///   // تنفيذ العملية
/// } else {
///   // إظهار رسالة خطأ
/// }
/// ```
library;

import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/snackbar_helper.dart';

/// كلاس خدمة الصلاحيات الموحدة
/// 
/// يوفر طرق ثابتة لإدارة جميع الصلاحيات في التطبيق
/// مع معالجة موحدة للأخطاء والرسائل
class PermissionService {
  
  /// طلب صلاحية التخزين
  /// 
  /// مطلوبة لحفظ الملفات والنسخ الاحتياطية
  /// Returns: true إذا تم منح الصلاحية، false إذا تم رفضها
  static Future<bool> requestStoragePermission() async {
    try {
      if (kIsWeb) {
        // في الويب لا نحتاج صلاحية التخزين
        return true;
      }

      final PermissionStatus status = await Permission.storage.request();
      
      if (status.isGranted) {
        if (kDebugMode) {
          print('✅ تم منح صلاحية التخزين');
        }
        return true;
      } else if (status.isDenied) {
        if (kDebugMode) {
          print('❌ تم رفض صلاحية التخزين');
        }
        return false;
      } else if (status.isPermanentlyDenied) {
        if (kDebugMode) {
          print('🚫 تم رفض صلاحية التخزين نهائياً');
        }
        // يمكن توجيه المستخدم لإعدادات التطبيق
        await openAppSettings();
        return false;
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في طلب صلاحية التخزين: $e');
      }
      return false;
    }
  }

  /// فحص صلاحية التخزين بدون طلب
  /// 
  /// Returns: true إذا كانت الصلاحية ممنوحة
  static Future<bool> checkStoragePermission() async {
    try {
      if (kIsWeb) return true;
      
      final PermissionStatus status = await Permission.storage.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص صلاحية التخزين: $e');
      }
      return false;
    }
  }

  /// طلب صلاحية الكاميرا
  /// 
  /// مطلوبة لمسح الباركود والتقاط الصور
  /// Returns: true إذا تم منح الصلاحية، false إذا تم رفضها
  static Future<bool> requestCameraPermission() async {
    try {
      if (kIsWeb) {
        // في الويب يتم طلب الصلاحية تلقائياً عند الحاجة
        return true;
      }

      final PermissionStatus status = await Permission.camera.request();
      
      if (status.isGranted) {
        if (kDebugMode) {
          print('✅ تم منح صلاحية الكاميرا');
        }
        return true;
      } else if (status.isDenied) {
        if (kDebugMode) {
          print('❌ تم رفض صلاحية الكاميرا');
        }
        return false;
      } else if (status.isPermanentlyDenied) {
        if (kDebugMode) {
          print('🚫 تم رفض صلاحية الكاميرا نهائياً');
        }
        await openAppSettings();
        return false;
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في طلب صلاحية الكاميرا: $e');
      }
      return false;
    }
  }

  /// فحص صلاحية الكاميرا بدون طلب
  static Future<bool> checkCameraPermission() async {
    try {
      if (kIsWeb) return true;
      
      final PermissionStatus status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص صلاحية الكاميرا: $e');
      }
      return false;
    }
  }

  /// طلب صلاحية الموقع
  /// 
  /// مطلوبة لتحديد موقع المتجر أو العميل
  /// Returns: true إذا تم منح الصلاحية، false إذا تم رفضها
  static Future<bool> requestLocationPermission() async {
    try {
      final PermissionStatus status = await Permission.location.request();
      
      if (status.isGranted) {
        if (kDebugMode) {
          print('✅ تم منح صلاحية الموقع');
        }
        return true;
      } else if (status.isDenied) {
        if (kDebugMode) {
          print('❌ تم رفض صلاحية الموقع');
        }
        return false;
      } else if (status.isPermanentlyDenied) {
        if (kDebugMode) {
          print('🚫 تم رفض صلاحية الموقع نهائياً');
        }
        await openAppSettings();
        return false;
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في طلب صلاحية الموقع: $e');
      }
      return false;
    }
  }

  /// فحص صلاحية الموقع بدون طلب
  static Future<bool> checkLocationPermission() async {
    try {
      final PermissionStatus status = await Permission.location.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص صلاحية الموقع: $e');
      }
      return false;
    }
  }

  /// طلب صلاحية جهات الاتصال
  /// 
  /// مطلوبة لاستيراد بيانات العملاء من جهات الاتصال
  /// Returns: true إذا تم منح الصلاحية، false إذا تم رفضها
  static Future<bool> requestContactsPermission() async {
    try {
      if (kIsWeb) {
        // جهات الاتصال غير متاحة في الويب
        return false;
      }

      final PermissionStatus status = await Permission.contacts.request();
      
      if (status.isGranted) {
        if (kDebugMode) {
          print('✅ تم منح صلاحية جهات الاتصال');
        }
        return true;
      } else if (status.isDenied) {
        if (kDebugMode) {
          print('❌ تم رفض صلاحية جهات الاتصال');
        }
        return false;
      } else if (status.isPermanentlyDenied) {
        if (kDebugMode) {
          print('🚫 تم رفض صلاحية جهات الاتصال نهائياً');
        }
        await openAppSettings();
        return false;
      }
      
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في طلب صلاحية جهات الاتصال: $e');
      }
      return false;
    }
  }

  /// فحص صلاحية جهات الاتصال بدون طلب
  static Future<bool> checkContactsPermission() async {
    try {
      if (kIsWeb) return false;
      
      final PermissionStatus status = await Permission.contacts.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص صلاحية جهات الاتصال: $e');
      }
      return false;
    }
  }

  /// طلب صلاحية الإشعارات
  /// 
  /// مطلوبة لإرسال تنبيهات للمستخدم
  /// Returns: true إذا تم منح الصلاحية، false إذا تم رفضها
  static Future<bool> requestNotificationPermission() async {
    try {
      final PermissionStatus status = await Permission.notification.request();
      
      if (status.isGranted) {
        if (kDebugMode) {
          print('✅ تم منح صلاحية الإشعارات');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ تم رفض صلاحية الإشعارات');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في طلب صلاحية الإشعارات: $e');
      }
      return false;
    }
  }

  /// فحص صلاحية الإشعارات بدون طلب
  static Future<bool> checkNotificationPermission() async {
    try {
      final PermissionStatus status = await Permission.notification.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فحص صلاحية الإشعارات: $e');
      }
      return false;
    }
  }

  /// طلب جميع الصلاحيات الأساسية
  /// 
  /// يطلب الصلاحيات المطلوبة للتشغيل الأساسي للتطبيق
  /// Returns: Map يحتوي على حالة كل صلاحية
  static Future<Map<String, bool>> requestEssentialPermissions() async {
    final Map<String, bool> results = <String, bool>{};
    
    // طلب صلاحية التخزين (أساسية)
    results['storage'] = await requestStoragePermission();
    
    // طلب صلاحية الإشعارات (مهمة)
    results['notification'] = await requestNotificationPermission();
    
    if (kDebugMode) {
      print('نتائج طلب الصلاحيات الأساسية: $results');
    }
    
    return results;
  }

  /// طلب جميع الصلاحيات الاختيارية
  /// 
  /// يطلب الصلاحيات الإضافية التي تحسن تجربة المستخدم
  /// Returns: Map يحتوي على حالة كل صلاحية
  static Future<Map<String, bool>> requestOptionalPermissions() async {
    final Map<String, bool> results = <String, bool>{};
    
    // طلب صلاحية الكاميرا (للباركود)
    results['camera'] = await requestCameraPermission();
    
    // طلب صلاحية الموقع (للعناوين)
    results['location'] = await requestLocationPermission();
    
    // طلب صلاحية جهات الاتصال (لاستيراد العملاء)
    results['contacts'] = await requestContactsPermission();
    
    if (kDebugMode) {
      print('نتائج طلب الصلاحيات الاختيارية: $results');
    }
    
    return results;
  }

  /// فحص حالة جميع الصلاحيات
  /// 
  /// Returns: Map يحتوي على حالة كل صلاحية
  static Future<Map<String, bool>> checkAllPermissions() async {
    final Map<String, bool> results = <String, bool>{};
    
    results['storage'] = await checkStoragePermission();
    results['camera'] = await checkCameraPermission();
    results['location'] = await checkLocationPermission();
    results['contacts'] = await checkContactsPermission();
    results['notification'] = await checkNotificationPermission();
    
    return results;
  }

  /// فتح إعدادات التطبيق
  /// 
  /// يوجه المستخدم لإعدادات التطبيق لمنح الصلاحيات يدوياً
  static Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      if (kDebugMode) {
        print('خطأ في فتح إعدادات التطبيق: $e');
      }
      return false;
    }
  }
}
