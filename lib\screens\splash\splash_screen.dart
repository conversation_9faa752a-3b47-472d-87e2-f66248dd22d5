import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_design_constants.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _fadeController;
  late Animation<double> _logoAnimation;
  late Animation<double> _fadeAnimation;
  bool _hasNavigated = false; // حماية من التنقل المتعدد

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    // تحكم في حركة الشعار
    _logoController = AnimationController(
      duration: AppDesignConstants.splashAnimationDuration,
      vsync: this,
    );

    // تحكم في التلاشي
    _fadeController = AnimationController(
      duration: AppDesignConstants.mediumAnimationDuration,
      vsync: this,
    );

    // حركة الشعار من الأسفل للأعلى مع تكبير
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // تأثير التلاشي
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startSplashSequence() async {
    // بدء الرسوم المتحركة
    await Future.delayed(const Duration(milliseconds: 300));
    _logoController.forward();

    await Future.delayed(const Duration(milliseconds: 500));
    _fadeController.forward();

    // انتظار مدة العرض الكاملة
    await Future.delayed(AppDesignConstants.splashDuration);

    // التحقق من حالة المستخدم والانتقال
    await _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    // حماية من التنقل المتعدد
    if (_hasNavigated || !mounted) return;
    _hasNavigated = true;

    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final bool hasViewedOnboarding = prefs.getBool(
            AppDesignConstants.hasViewedOnboardingKey,
          ) ??
          false;

      if (!mounted) return;

      if (hasViewedOnboarding) {
        // المستخدم شاهد الـ onboarding من قبل، انتقل للشاشة الرئيسية
        context.go('/');
      } else {
        // مستخدم جديد، انتقل لشاشة الـ onboarding
        context.go('/onboarding');
      }
    } catch (e) {
      // في حالة حدوث خطأ، انتقل للشاشة الرئيسية
      debugPrint('خطأ في التنقل من splash screen: $e');
      if (!mounted) return;

      // إعادة تعيين الحماية في حالة الخطأ
      _hasNavigated = false;

      context.go('/');
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تعيين شريط الحالة شفاف
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: SafeArea(
          child: Column(
            children: <Widget>[
              // المساحة العلوية
              const Spacer(flex: 2),

              // الشعار والنص الرئيسي
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    // أيقونة الشعار مع حركة - تصميم جميل لأسامة ماركت
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (BuildContext context, Widget? child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: <Color>[
                                  AppDesignConstants.primaryColor,
                                  AppDesignConstants.primaryColor
                                      .withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: <BoxShadow>[
                                BoxShadow(
                                  color: AppDesignConstants.primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.storefront_rounded,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(
                        height: AppDesignConstants.extraLargePadding),

                    // اسم التطبيق مع تأثير الكتابة - أسامة ماركت
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: <Widget>[
                          // النص الرئيسي
                          AnimatedTextKit(
                            animatedTexts: <AnimatedText>[
                              TyperAnimatedText(
                                'أسامة ماركت',
                                textStyle: AppDesignConstants.splashTitleStyle,
                                speed: const Duration(milliseconds: 120),
                              ),
                            ],
                            totalRepeatCount: 1,
                            displayFullTextOnTap: true,
                          ),
                          const SizedBox(height: 8),
                          // خط تحت الاسم
                          Container(
                            width: 80,
                            height: 3,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: <Color>[
                                  AppDesignConstants.primaryColor,
                                  AppDesignConstants.accentColor,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: AppDesignConstants.defaultPadding),

                    // الشعار التوضيحي
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: const Text(
                        'إدارة ذكية لمتجرك',
                        style: AppDesignConstants.splashSubtitleStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              // مؤشر التحميل
              Expanded(
                flex: 1,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      SpinKitWave(
                        color: AppDesignConstants.primaryColor,
                        size: 30.0,
                      ),
                      SizedBox(height: AppDesignConstants.defaultPadding),
                      Text(
                        'جاري التحميل...',
                        style: TextStyle(
                          fontFamily: 'Cairo',
                          fontSize: AppDesignConstants.captionFontSize,
                          color: AppDesignConstants.textSecondaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // المساحة السفلية
              const Spacer(flex: 1),
            ],
          ),
        ),
      ),
    );
  }
}
