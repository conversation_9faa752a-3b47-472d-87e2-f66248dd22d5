/// خدمة إدارة الاعتماديات (Dependency Injection) لأسامة ماركت
///
/// تستخدم مكتبة get_it لإدارة الاعتماديات وتوفير نمط Singleton
/// للخدمات المشتركة في التطبيق
///
/// المطور: فريق أسامة ماركت
/// التاريخ: 2024
library dependency_injection;

import 'package:get_it/get_it.dart';

import '../services/database_service.dart';
import '../services/product_service.dart';
import '../services/customer_service.dart';
import '../services/sale_service.dart';
import '../services/purchase_service.dart';
import '../services/supplier_service.dart';
import '../services/backup_service.dart';
import '../services/notification_service.dart';
import '../providers/product_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/sale_provider.dart';
import '../providers/purchase_provider.dart';
import '../providers/supplier_provider.dart';
import '../providers/analytics_provider.dart';
import '../providers/backup_provider.dart';

/// مثيل GetIt العام للوصول للاعتماديات
final GetIt getIt = GetIt.instance;

/// تهيئة جميع الاعتماديات في التطبيق
///
/// يجب استدعاء هذه الدالة في main() قبل تشغيل التطبيق
Future<void> setupDependencyInjection() async {
  // تسجيل الخدمات كـ Singletons
  await _registerServices();
  
  // تسجيل المزودات كـ Factories (مثيل جديد في كل مرة)
  _registerProviders();
}

/// تسجيل جميع الخدمات كـ Singletons
Future<void> _registerServices() async {
  // خدمة قاعدة البيانات - يجب أن تكون أول خدمة
  getIt.registerSingletonAsync<DatabaseService>(
    () async {
      final DatabaseService service = DatabaseService.instance;
      await service.database; // تهيئة قاعدة البيانات
      return service;
    },
  );

  // انتظار تهيئة قاعدة البيانات قبل المتابعة
  await getIt.isReady<DatabaseService>();

  // خدمات البيانات
  getIt.registerSingleton<ProductService>(ProductService());
  getIt.registerSingleton<CustomerService>(CustomerService());
  getIt.registerSingleton<SaleService>(SaleService());
  getIt.registerSingleton<PurchaseService>(PurchaseService());
  getIt.registerSingleton<SupplierService>(SupplierService());

  // خدمات النظام
  getIt.registerSingleton<BackupService>(BackupService());
  getIt.registerSingleton<NotificationService>(NotificationService.instance);
}

/// تسجيل جميع المزودات كـ Factories
void _registerProviders() {
  // مزودات البيانات - يتم حقن الخدمات المطلوبة
  getIt.registerFactory<ProductProvider>(
    () => ProductProvider.withService(getIt<ProductService>()),
  );
  
  getIt.registerFactory<CustomerProvider>(
    () => CustomerProvider.withService(getIt<CustomerService>()),
  );
  
  getIt.registerFactory<SaleProvider>(
    () => SaleProvider.withService(getIt<SaleService>()),
  );
  
  getIt.registerFactory<PurchaseProvider>(
    () => PurchaseProvider.withService(getIt<PurchaseService>()),
  );
  
  getIt.registerFactory<SupplierProvider>(
    () => SupplierProvider.withService(getIt<SupplierService>()),
  );

  // مزودات النظام
  getIt.registerFactory<AnalyticsProvider>(
    () => AnalyticsProvider(),
  );
  
  getIt.registerFactory<BackupProvider>(
    () => BackupProvider.withService(getIt<BackupService>()),
  );
}

/// إعادة تعيين جميع الاعتماديات (للاختبارات)
Future<void> resetDependencyInjection() async {
  await getIt.reset();
}

/// تسجيل خدمات وهمية للاختبارات
void setupTestDependencies() {
  // يمكن إضافة Mock Services هنا للاختبارات
  // مثال:
  // getIt.registerSingleton<ProductService>(MockProductService());
}

/// التحقق من تهيئة الاعتماديات
bool get isDependencyInjectionReady {
  try {
    getIt<DatabaseService>();
    getIt<ProductService>();
    getIt<CustomerService>();
    return true;
  } catch (e) {
    return false;
  }
}

/// الحصول على خدمة معينة
T getService<T extends Object>() {
  return getIt<T>();
}

/// الحصول على مزود معين
T getProvider<T extends Object>() {
  return getIt<T>();
}

/// إنشاء مزود جديد مع حقن الاعتماديات
T createProvider<T extends Object>() {
  return getIt<T>();
}
