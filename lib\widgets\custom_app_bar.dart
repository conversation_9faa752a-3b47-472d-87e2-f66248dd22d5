import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../config/app_colors.dart';

/// AppBar مخصص مع زر الرجوع الذكي
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool centerTitle;
  final Widget? leading;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.onBackPressed,
    this.centerTitle = true,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: centerTitle,
      title: Text(
        title,
        style: const TextStyle(
          fontFamily: 'Tajawal',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      leading: _buildLeading(context),
      actions: actions,
      automaticallyImplyLeading: false, // نتحكم نحن في زر الرجوع
    );
  }

  Widget? _buildLeading(BuildContext context) {
    if (leading != null) {
      return leading;
    }

    if (!showBackButton) {
      return null;
    }

    // تحديد نوع زر الرجوع حسب الموقع
    final String currentRoute = GoRouterState.of(context).uri.path;

    // زر القائمة فقط في الشاشة الرئيسية الأساسية
    if (currentRoute == '/' || currentRoute == '/dashboard') {
      return IconButton(
        icon: const Icon(
          Icons.menu,
          color: Colors.white,
          size: 24,
        ),
        onPressed: () => Scaffold.of(context).openDrawer(),
        tooltip: 'القائمة',
      );
    } else {
      // في جميع الشاشات الأخرى - زر الرجوع
      return IconButton(
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Colors.white,
          size: 20,
        ),
        onPressed: onBackPressed ?? () => _handleBackPress(context),
        tooltip: 'رجوع',
      );
    }
  }

  void _handleBackPress(BuildContext context) {
    // التحقق من إمكانية الرجوع
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      // الرجوع للصفحة الرئيسية
      context.go('/');
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// AppBar للصفحة الرئيسية مع drawer
class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;

  const HomeAppBar({
    super.key,
    required this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.store,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontFamily: 'Tajawal',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
      leading: Builder(
        builder: (BuildContext context) => IconButton(
          icon: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(
              Icons.menu,
              color: Colors.white,
              size: 22,
            ),
          ),
          onPressed: () => Scaffold.of(context).openDrawer(),
          tooltip: 'الإجراءات السريعة',
        ),
      ),
      actions: <Widget>[
        ...?actions,
        // زر الإشعارات
        IconButton(
          icon: Stack(
            children: <Widget>[
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.notifications_outlined,
                  color: Colors.white,
                  size: 22,
                ),
              ),
              // نقطة الإشعار
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
          onPressed: () {
            // TODO: فتح صفحة الإشعارات
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('الإشعارات قريباً...'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          tooltip: 'الإشعارات',
        ),
        const SizedBox(width: 8),
      ],
      automaticallyImplyLeading: false,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
