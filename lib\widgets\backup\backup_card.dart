import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// ودجت موحد لعرض بطاقة النسخة الاحتياطية
/// Unified widget for displaying backup card
class BackupCard extends StatelessWidget {
  /// اسم النسخة الاحتياطية
  final String name;
  
  /// تاريخ إنشاء النسخة
  final DateTime createdAt;
  
  /// حجم النسخة (بالبايت)
  final int? size;
  
  /// نوع النسخة (محلية أو سحابية)
  final BackupType type;
  
  /// هل النسخة متاحة
  final bool isAvailable;
  
  /// دالة الاستعادة
  final VoidCallback? onRestore;
  
  /// دالة الحذف
  final VoidCallback? onDelete;
  
  /// دالة التحميل (للنسخ السحابية)
  final VoidCallback? onDownload;
  
  /// دالة المشاركة
  final VoidCallback? onShare;

  const BackupCard({
    super.key,
    required this.name,
    required this.createdAt,
    this.size,
    required this.type,
    this.isAvailable = true,
    this.onRestore,
    this.onDelete,
    this.onDownload,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildHeader(),
            const SizedBox(height: 12),
            _buildInfo(),
            const SizedBox(height: 16),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: <Widget>[
        Icon(
          _getTypeIcon(),
          color: _getTypeColor(),
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                _getTypeLabel(),
                style: TextStyle(
                  fontSize: 12,
                  color: _getTypeColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        if (!isAvailable)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'غير متاح',
              style: TextStyle(
                fontSize: 10,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInfo() {
    final DateFormat dateFormat = DateFormat('dd/MM/yyyy HH:mm', 'ar');
    
    return Column(
      children: <Widget>[
        _buildInfoRow(
          icon: Icons.access_time,
          label: 'تاريخ الإنشاء',
          value: dateFormat.format(createdAt),
        ),
        if (size != null) ...<Widget>[
          const SizedBox(height: 8),
          _buildInfoRow(
            icon: Icons.storage,
            label: 'الحجم',
            value: _formatFileSize(size!),
          ),
        ],
        const SizedBox(height: 8),
        _buildInfoRow(
          icon: Icons.schedule,
          label: 'منذ',
          value: _getTimeAgo(),
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: <Widget>[
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: <Widget>[
        if (onRestore != null && isAvailable)
          _buildActionButton(
            icon: Icons.restore,
            label: 'استعادة',
            color: Colors.blue,
            onPressed: onRestore!,
          ),
        if (onDownload != null && type == BackupType.cloud)
          _buildActionButton(
            icon: Icons.download,
            label: 'تحميل',
            color: Colors.green,
            onPressed: onDownload!,
          ),
        if (onShare != null && type == BackupType.local)
          _buildActionButton(
            icon: Icons.share,
            label: 'مشاركة',
            color: Colors.orange,
            onPressed: onShare!,
          ),
        if (onDelete != null)
          _buildActionButton(
            icon: Icons.delete,
            label: 'حذف',
            color: Colors.red,
            onPressed: onDelete!,
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: isAvailable ? onPressed : null,
          icon: Icon(icon, size: 16),
          label: Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (type) {
      case BackupType.local:
        return Icons.storage;
      case BackupType.cloud:
        return Icons.cloud;
    }
  }

  Color _getTypeColor() {
    switch (type) {
      case BackupType.local:
        return Colors.blue;
      case BackupType.cloud:
        return Colors.green;
    }
  }

  String _getTypeLabel() {
    switch (type) {
      case BackupType.local:
        return 'نسخة محلية';
      case BackupType.cloud:
        return 'نسخة سحابية';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String _getTimeAgo() {
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

/// أنواع النسخ الاحتياطية
enum BackupType {
  local,
  cloud,
}
