import 'package:flutter/material.dart';
import 'package:fast_contacts/fast_contacts.dart';
import 'package:permission_handler/permission_handler.dart';

class ImportContactsDialog extends StatefulWidget {
  final String type; // 'customer' أو 'supplier'
  final Function(List<Contact>) onContactsSelected;

  const ImportContactsDialog({
    super.key,
    required this.type,
    required this.onContactsSelected,
  });

  @override
  State<ImportContactsDialog> createState() => _ImportContactsDialogState();
}

class _ImportContactsDialogState extends State<ImportContactsDialog> {
  List<Contact> _allContacts = <Contact>[];
  List<Contact> _filteredContacts = <Contact>[];
  List<Contact> _selectedContacts = <Contact>[];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  bool _hasPermission = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _requestPermissionAndLoadContacts();
  }

  Future<void> _requestPermissionAndLoadContacts() async {
    try {
      // طلب إذن الوصول لجهات الاتصال
      final PermissionStatus permission = await Permission.contacts.request();

      if (permission == PermissionStatus.granted) {
        setState(() {
          _hasPermission = true;
        });
        await _loadContacts();
      } else if (permission == PermissionStatus.denied) {
        setState(() {
          _hasPermission = false;
          _errorMessage =
              'تم رفض إذن الوصول لجهات الاتصال. يرجى منح الإذن من إعدادات التطبيق.';
          _isLoading = false;
        });
      } else if (permission == PermissionStatus.permanentlyDenied) {
        setState(() {
          _hasPermission = false;
          _errorMessage =
              'تم رفض إذن الوصول لجهات الاتصال نهائياً. يرجى تفعيله من إعدادات الجهاز.';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasPermission = false;
        _errorMessage = 'حدث خطأ في طلب الإذن: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadContacts() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // تحميل جهات الاتصال الحقيقية
      final List<Contact> contactsList = await FastContacts.getAllContacts();

      setState(() {
        _allContacts = contactsList;
        _filteredContacts = contactsList;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ في تحميل جهات الاتصال: $e';
        _isLoading = false;
      });
    }
  }

  void _filterContacts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredContacts = _allContacts;
      } else {
        _filteredContacts = _allContacts.where((Contact contact) {
          final String name = contact.displayName.toLowerCase();
          final String phone = contact.phones.isNotEmpty
              ? contact.phones.first.number.toLowerCase()
              : '';
          return name.contains(query.toLowerCase()) ||
              phone.contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _toggleContactSelection(Contact contact) {
    setState(() {
      if (_selectedContacts.contains(contact)) {
        _selectedContacts.remove(contact);
      } else {
        _selectedContacts.add(contact);
      }
    });
  }

  void _selectAllContacts() {
    setState(() {
      _selectedContacts = List.from(_filteredContacts);
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedContacts.clear();
    });
  }

  void _importSelectedContacts() {
    if (_selectedContacts.isNotEmpty) {
      widget.onContactsSelected(_selectedContacts);
      Navigator.pop(context);
    }
  }

  Future<void> _openAppSettings() async {
    await openAppSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: <Widget>[
              // العنوان
              Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      'استيراد ${widget.type == 'customer' ? 'العملاء' : 'الموردين'}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // المحتوى الرئيسي
              Expanded(
                child: _buildMainContent(),
              ),

              // الأزرار السفلية
              if (_hasPermission && !_isLoading && _filteredContacts.isNotEmpty)
                _buildBottomButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل جهات الاتصال...'),
          ],
        ),
      );
    }

    if (!_hasPermission || _errorMessage != null) {
      return _buildErrorWidget();
    }

    if (_filteredContacts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(Icons.contacts, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد جهات اتصال',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: <Widget>[
        // شريط البحث
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'البحث في جهات الاتصال',
            hintText: 'ادخل الاسم أو رقم الهاتف',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: _filterContacts,
        ),

        const SizedBox(height: 16),

        // عداد المحدد
        Row(
          children: <Widget>[
            Text(
              'المحدد: ${_selectedContacts.length} من ${_filteredContacts.length}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: _selectAllContacts,
              child: const Text('تحديد الكل'),
            ),
            TextButton(
              onPressed: _clearSelection,
              child: const Text('إلغاء التحديد'),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // قائمة جهات الاتصال
        Expanded(
          child: ListView.builder(
            itemCount: _filteredContacts.length,
            itemBuilder: (BuildContext context, int index) {
              final Contact contact = _filteredContacts[index];
              return _buildContactItem(contact);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? 'حدث خطأ غير متوقع',
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16, color: Colors.red),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _openAppSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('فتح إعدادات التطبيق'),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _requestPermissionAndLoadContacts,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(Contact contact) {
    final bool isSelected = _selectedContacts.contains(contact);
    final String primaryPhone =
        contact.phones.isNotEmpty ? contact.phones.first.number : '';

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isSelected ? Colors.blue : Colors.grey[300],
          child: Text(
            contact.displayName.isNotEmpty
                ? contact.displayName[0].toUpperCase()
                : '؟',
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          contact.displayName.isNotEmpty ? contact.displayName : 'بدون اسم',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          primaryPhone.isNotEmpty ? primaryPhone : 'لا يوجد رقم هاتف',
          style: TextStyle(color: Colors.grey[600]),
        ),
        trailing: Checkbox(
          value: isSelected,
          onChanged: (bool? value) {
            _toggleContactSelection(contact);
          },
          activeColor: Colors.blue,
        ),
        onTap: () => _toggleContactSelection(contact),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        children: <Widget>[
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('إلغاء'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed:
                  _selectedContacts.isNotEmpty ? _importSelectedContacts : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text(
                'استيراد (${_selectedContacts.length})',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
