import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../config/app_dimensions.dart';

import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../widgets/conditional_bottom_nav.dart'; // لـ MainScreenWrapper
import '../../utils/navigation_manager.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/formatters.dart';
import '../../utils/date_helper.dart';
import '../../providers/sale_provider.dart';
import '../../models/sale.dart';

/// شاشة قائمة المبيعات
class SaleListScreen extends StatefulWidget {
  const SaleListScreen({super.key});

  @override
  State<SaleListScreen> createState() => _SaleListScreenState();
}

class _SaleListScreenState extends State<SaleListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'date';
  bool _sortAscending = false;
  String _filterBy = 'all';
  DateTimeRange? _dateRange;

  /// Helper method to parse date string to DateTime
  DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  @override
  void initState() {
    super.initState();
    _loadSales();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadSales() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SaleProvider>().loadSales();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: MainScreenWrapper(
          title: 'المبيعات',
          needsDrawer: false, // زر القائمة فقط في الشاشة الرئيسية
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.add_shopping_cart),
              onPressed: () => context.go('/sales/add'),
              tooltip: 'إضافة فاتورة بيع جديدة',
            ),
          ],
          child: Container(
            color: AppColors.background,
            child: Column(
              children: <Widget>[
                _buildSearchAndFilters(),
                _buildSummaryCard(),
                Expanded(child: _buildSalesList()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: <Widget>[
          // شريط البحث
          TextFormField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في المبيعات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearSearch,
                    )
                  : null,
              filled: true,
              fillColor: AppColors.surfaceVariant,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                borderSide: BorderSide.none,
              ),
            ),
            onChanged: (String value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),

          const SizedBox(height: AppDimensions.paddingM),

          // أزرار التصفية والترتيب
          Row(
            children: <Widget>[
              Expanded(child: _buildFilterChips()),
              const SizedBox(width: AppDimensions.paddingM),
              _buildSortButton(),
            ],
          ),

          // نطاق التاريخ المحدد
          if (_dateRange != null) ...<Widget>[
            const SizedBox(height: AppDimensions.paddingS),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const Icon(
                    Icons.date_range,
                    size: 16,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${DateHelper.formatDate(_dateRange!.start)} - ${DateHelper.formatDate(_dateRange!.end)}',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _dateRange = null;
                      });
                    },
                    child: const Icon(
                      Icons.close,
                      size: 16,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: <Widget>[
          _buildFilterChip('all', 'الكل'),
          _buildFilterChip('today', 'اليوم'),
          _buildFilterChip('week', 'هذا الأسبوع'),
          _buildFilterChip('month', 'هذا الشهر'),
          _buildFilterChip('completed', 'مكتملة'),
          _buildFilterChip('pending', 'معلقة'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final bool isSelected = _filterBy == value;
    return Padding(
      padding: const EdgeInsets.only(left: AppDimensions.paddingS),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (bool selected) {
          setState(() {
            _filterBy = value;
          });
        },
        backgroundColor: AppColors.surfaceVariant,
        selectedColor: AppColors.primary.withOpacity(0.2),
        checkmarkColor: AppColors.primary,
        labelStyle: TextStyle(
          color: isSelected ? AppColors.primary : AppColors.textSecondary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildSortButton() {
    return PopupMenuButton<String>(
      icon: Icon(
        _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
        color: AppColors.primary,
      ),
      onSelected: (String value) {
        setState(() {
          if (_sortBy == value) {
            _sortAscending = !_sortAscending;
          } else {
            _sortBy = value;
            _sortAscending = value == 'date' ? false : true;
          }
        });
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        const PopupMenuItem(
          value: 'date',
          child: Text('ترتيب حسب التاريخ'),
        ),
        const PopupMenuItem(
          value: 'total',
          child: Text('ترتيب حسب المبلغ'),
        ),
        const PopupMenuItem(
          value: 'customer',
          child: Text('ترتيب حسب العميل'),
        ),
        const PopupMenuItem(
          value: 'invoice',
          child: Text('ترتيب حسب رقم الفاتورة'),
        ),
      ],
    );
  }

  Widget _buildSummaryCard() {
    return Consumer<SaleProvider>(
      builder:
          (BuildContext context, SaleProvider saleProvider, Widget? child) {
        final List<Sale> filteredSales = _getFilteredSales(saleProvider.sales);
        final double totalAmount = filteredSales.fold<double>(
          0,
          (double sum, Sale sale) => sum + (sale.totalAmount ?? 0),
        );
        final int completedSales = filteredSales
            .where((Sale sale) => sale.status == 'completed')
            .length;

        return Container(
          margin: const EdgeInsets.all(AppDimensions.paddingM),
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradient,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                child: _buildSummaryItem(
                  'إجمالي المبيعات',
                  '${filteredSales.length}',
                  Icons.receipt_long,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withOpacity(0.3),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'المبيعات المكتملة',
                  '$completedSales',
                  Icons.check_circle,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withOpacity(0.3),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'إجمالي المبلغ',
                  Formatters.formatCurrency(totalAmount),
                  Icons.monetization_on,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: <Widget>[
        Icon(
          icon,
          color: Colors.white,
          size: AppDimensions.iconM,
        ),
        const SizedBox(height: AppDimensions.paddingXS),
        Text(
          value,
          style: AppStyles.titleMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppStyles.labelSmall.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSalesList() {
    return Consumer<SaleProvider>(
      builder:
          (BuildContext context, SaleProvider saleProvider, Widget? child) {
        if (saleProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final List<Sale> filteredSales = _getFilteredSales(saleProvider.sales);

        if (filteredSales.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            await saleProvider.loadSales();
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            itemCount: filteredSales.length,
            itemBuilder: (BuildContext context, int index) {
              final Sale sale = filteredSales[index];
              return _buildSaleCard(sale);
            },
          ),
        );
      },
    );
  }

  Widget _buildSaleCard(Sale sale) {
    final bool isCompleted = sale.status == 'completed';
    final bool isPending = sale.status == 'pending';

    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: AppDimensions.elevationS,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        side: isCompleted
            ? const BorderSide(color: AppColors.success, width: 1)
            : isPending
                ? const BorderSide(color: AppColors.warning, width: 1)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => context.go('/sales/details/${sale.id}'),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'فاتورة رقم: ${sale.invoiceNumber ?? sale.id}',
                          style: AppStyles.titleMedium,
                        ),
                        const SizedBox(height: AppDimensions.paddingXS),
                        Text(
                          sale.customerName ?? 'عميل نقدي',
                          style: AppStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusBadge(sale.status ?? 'draft'),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildInfoItem(
                      'التاريخ',
                      sale.date ?? '-',
                      Icons.calendar_today,
                      AppColors.info,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      'المبلغ الإجمالي',
                      Formatters.formatCurrency(sale.totalAmount ?? 0),
                      Icons.monetization_on,
                      AppColors.success,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(0.1),
                          borderRadius:
                              BorderRadius.circular(AppDimensions.radiusS),
                        ),
                        child: Text(
                          Formatters.formatPaymentMethod(
                              sale.paymentMethod ?? 'نقداً'),
                          style: AppStyles.labelSmall.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(Icons.visibility, size: 18),
                        onPressed: () =>
                            context.go('/sales/details/${sale.id}'),
                        tooltip: 'عرض',
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit, size: 18),
                        onPressed: () => context.go('/sales/edit/${sale.id}'),
                        tooltip: 'تعديل',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, size: 18),
                        onPressed: () => _deleteSale(sale),
                        tooltip: 'حذف',
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    String text;
    IconData icon;

    switch (status.toLowerCase()) {
      case 'completed':
        color = AppColors.success;
        text = 'مكتملة';
        icon = Icons.check_circle;
        break;
      case 'pending':
        color = AppColors.warning;
        text = 'معلقة';
        icon = Icons.pending;
        break;
      case 'cancelled':
        color = AppColors.error;
        text = 'ملغية';
        icon = Icons.cancel;
        break;
      default:
        color = AppColors.textSecondary;
        text = 'مسودة';
        icon = Icons.drafts;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: AppStyles.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
      String label, String value, IconData icon, Color color) {
    return Row(
      children: <Widget>[
        Icon(icon, size: 16, color: color),
        const SizedBox(width: AppDimensions.paddingXS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                label,
                style: AppStyles.labelSmall,
              ),
              Text(
                value,
                style: AppStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            'لا توجد مبيعات',
            style: AppStyles.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            'ابدأ بإضافة فواتير بيع جديدة',
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          ElevatedButton.icon(
            onPressed: () => context.go('/sales/add'),
            icon: const Icon(Icons.add_shopping_cart),
            label: const Text('إضافة فاتورة بيع'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  List<Sale> _getFilteredSales(List<Sale> sales) {
    List<Sale> filtered = sales.where((Sale sale) {
      // تصفية البحث
      if (_searchQuery.isNotEmpty) {
        final String query = _searchQuery.toLowerCase();
        final String invoiceNumber = (sale.invoiceNumber ?? '').toLowerCase();
        final String customerName = (sale.customerName ?? '').toLowerCase();

        if (!invoiceNumber.contains(query) && !customerName.contains(query)) {
          return false;
        }
      }

      // تصفية حسب التاريخ
      if (_dateRange != null && sale.date != null) {
        final DateTime? saleDate = _parseDate(sale.date);
        if (saleDate != null) {
          if (saleDate.isBefore(_dateRange!.start) ||
              saleDate.isAfter(_dateRange!.end)) {
            return false;
          }
        }
      }

      // تصفية حسب الفترة
      if (sale.date != null) {
        final DateTime? saleDate = _parseDate(sale.date);
        switch (_filterBy) {
          case 'today':
            return saleDate != null ? DateHelper.isToday(saleDate) : false;
          case 'week':
            return saleDate != null ? DateHelper.isThisWeek(saleDate) : false;
          case 'month':
            return saleDate != null ? DateHelper.isThisMonth(saleDate) : false;
          case 'completed':
            return sale.status == 'completed';
          case 'pending':
            return sale.status == 'pending';
        }
      }

      return true;
    }).toList();

    // ترتيب النتائج
    filtered.sort((Sale a, Sale b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'date':
          final DateTime dateA = _parseDate(a.date) ?? DateTime.now();
          final DateTime dateB = _parseDate(b.date) ?? DateTime.now();
          comparison = dateA.compareTo(dateB);
          break;
        case 'total':
          comparison = (a.totalAmount ?? 0).compareTo(b.totalAmount ?? 0);
          break;
        case 'customer':
          comparison = (a.customerName ?? '').compareTo(b.customerName ?? '');
          break;
        case 'invoice':
          comparison = (a.invoiceNumber ?? '').compareTo(b.invoiceNumber ?? '');
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  void _selectDateRange() async {
    final DateTimeRange<DateTime>? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: _dateRange,
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
        _filterBy = 'all'; // إعادة تعيين التصفية
      });
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportSales();
        break;
      case 'print':
        _printReport();
        break;
      case 'analytics':
        _showAnalytics();
        break;
    }
  }

  void _exportSales() {
    // TODO: تنفيذ تصدير المبيعات
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ التصدير قريباً');
  }

  void _printReport() {
    // TODO: تنفيذ طباعة التقرير
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ الطباعة قريباً');
  }

  void _showAnalytics() {
    // TODO: عرض التحليلات
    SnackBarHelper.showInfo(context, 'سيتم عرض التحليلات قريباً');
  }

  void _deleteSale(Sale sale) async {
    final bool? confirmed = await EnhancedConfirmationDialog.showDelete(
      context,
      itemName: 'فاتورة رقم ${sale.invoiceNumber ?? sale.id}',
    );

    if (confirmed == true) {
      try {
        await context.read<SaleProvider>().deleteSale(sale.id!);
        SnackBarHelper.showSuccess(context, 'تم حذف الفاتورة بنجاح');
      } catch (e) {
        SnackBarHelper.showError(context, 'فشل في حذف الفاتورة: $e');
      }
    }
  }
}
