{"@@locale": "en", "@@context": "Osama Market - Inventory Management System", "appTitle": "Osama Market", "@appTitle": {"description": "Main application title"}, "appSubtitle": "Smart Management for Your Store", "@appSubtitle": {"description": "Application subtitle"}, "dashboard": "Dashboard", "sales": "Sales", "purchases": "Purchases", "products": "Products", "customers": "Customers", "suppliers": "Suppliers", "inventory": "Inventory", "reports": "Reports", "settings": "Settings", "backup": "Backup", "add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "name": "Name", "description": "Description", "price": "Price", "quantity": "Quantity", "total": "Total", "date": "Date", "time": "Time", "phone": "Phone", "address": "Address", "email": "Email", "notes": "Notes", "currency": "SAR", "@currency": {"description": "Saudi Riyal currency symbol"}, "welcomeMessage": "Welcome to Osama Market", "@welcomeMessage": {"description": "Welcome message in the app"}, "noDataFound": "No data found", "@noDataFound": {"description": "No data message"}, "confirmDelete": "Are you sure you want to delete?", "@confirmDelete": {"description": "Delete confirmation message"}, "operationSuccessful": "Operation completed successfully", "@operationSuccessful": {"description": "Success operation message"}, "operationFailed": "Operation failed", "@operationFailed": {"description": "Failed operation message"}, "permissionRequired": "Permission Required", "@permissionRequired": {"description": "Permission request message"}, "permissionDenied": "Permission Denied", "@permissionDenied": {"description": "Permission denied message"}, "storagePermission": "Storage Permission", "@storagePermission": {"description": "Storage access permission"}, "cameraPermission": "Camera Permission", "@cameraPermission": {"description": "Camera access permission"}, "locationPermission": "Location Permission", "@locationPermission": {"description": "Location access permission"}, "contactsPermission": "Contacts Permission", "@contactsPermission": {"description": "Contacts access permission"}, "all": "All", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "sortByName": "Sort by Name", "sortByQuantity": "Sort by Quantity", "sortByPrice": "Sort by Price", "sortByDate": "Sort by Date", "export": "Export", "import": "Import", "importFromCsv": "Import from CSV", "lowStockAlert": "Low Stock Alert", "searchProducts": "Search for product...", "noProductsFound": "No products found", "addNewProduct": "Add New Product", "productDeleted": "Product deleted successfully", "deleteProductFailed": "Failed to delete product", "product": "Product", "salePrice": "Sale Price", "retailPrice": "Retail Price", "wholesalePrice": "Wholesale Price", "category": "Category", "unit": "Unit", "barcode": "Barcode"}