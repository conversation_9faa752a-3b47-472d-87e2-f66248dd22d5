import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import '../utils/settings_helper.dart';

/// فئة مساعدة للأمان والحماية
class SecurityHelper {
  static const String _saltKey = 'inventory_app_salt';
  static const int _saltLength = 32;
  static const int _hashIterations = 10000;

  /// تشفير كلمة المرور
  static String hashPassword(String password, {String? salt}) {
    salt ??= _generateSalt();

    final Uint8List bytes = utf8.encode(password + salt);
    Digest digest = sha256.convert(bytes);

    // تطبيق التشفير عدة مرات لزيادة الأمان
    for (int i = 0; i < _hashIterations; i++) {
      digest = sha256.convert(digest.bytes);
    }

    return '$salt:${digest.toString()}';
  }

  /// التحقق من كلمة المرور
  static bool verifyPassword(String password, String hashedPassword) {
    try {
      final List<String> parts = hashedPassword.split(':');
      if (parts.length != 2) return false;

      final String salt = parts[0];
      final String hash = parts[1];

      final String newHash = hashPassword(password, salt: salt);
      return newHash.split(':')[1] == hash;
    } catch (e) {
      return false;
    }
  }

  /// إنشاء salt عشوائي
  static String _generateSalt() {
    final Random random = Random.secure();
    final List<int> bytes =
        List<int>.generate(_saltLength, (int i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// تشفير البيانات
  static String encryptData(String data, String key) {
    try {
      // تشفير بسيط باستخدام XOR
      final Uint8List keyBytes = utf8.encode(key);
      final Uint8List dataBytes = utf8.encode(data);
      final List<int> encrypted = <int>[];

      for (int i = 0; i < dataBytes.length; i++) {
        encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return base64.encode(encrypted);
    } catch (e) {
      return data; // إرجاع البيانات الأصلية في حالة الخطأ
    }
  }

  /// فك تشفير البيانات
  static String decryptData(String encryptedData, String key) {
    try {
      final Uint8List keyBytes = utf8.encode(key);
      final Uint8List encryptedBytes = base64.decode(encryptedData);
      final List<int> decrypted = <int>[];

      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return utf8.decode(decrypted);
    } catch (e) {
      return encryptedData; // إرجاع البيانات المشفرة في حالة الخطأ
    }
  }

  /// إنشاء مفتاح تشفير عشوائي
  static String generateEncryptionKey({int length = 32}) {
    final Random random = Random.secure();
    final List<int> bytes =
        List<int>.generate(length, (int i) => random.nextInt(256));
    return base64.encode(bytes);
  }

  /// إنشاء رمز مميز (token)
  static String generateToken({int length = 32}) {
    const String chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final Random random = Random.secure();
    return String.fromCharCodes(Iterable.generate(
        length, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  /// إنشاء معرف فريد
  static String generateUniqueId() {
    final int timestamp = DateTime.now().millisecondsSinceEpoch;
    final int random = Random().nextInt(999999);
    return '${timestamp}_$random';
  }

  /// تشفير البيانات الحساسة
  static Future<String> encryptSensitiveData(String data) async {
    final String key = await _getOrCreateEncryptionKey();
    return encryptData(data, key);
  }

  /// فك تشفير البيانات الحساسة
  static Future<String> decryptSensitiveData(String encryptedData) async {
    final String key = await _getOrCreateEncryptionKey();
    return decryptData(encryptedData, key);
  }

  /// الحصول على أو إنشاء مفتاح التشفير
  static Future<String> _getOrCreateEncryptionKey() async {
    String? key = SettingsHelper.getEncryptionKey();

    if (key == null) {
      key = generateEncryptionKey();
      await SettingsHelper.setEncryptionKey(key);
    }

    return key;
  }

  /// التحقق من قوة كلمة المرور
  static PasswordStrength checkPasswordStrength(String password) {
    if (password.length < 6) {
      return PasswordStrength.weak;
    }

    int score = 0;

    // طول كلمة المرور
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;

    // احتواء على أحرف صغيرة
    if (password.contains(RegExp(r'[a-z]'))) score++;

    // احتواء على أحرف كبيرة
    if (password.contains(RegExp(r'[A-Z]'))) score++;

    // احتواء على أرقام
    if (password.contains(RegExp(r'[0-9]'))) score++;

    // احتواء على رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;

    if (score < 3) return PasswordStrength.weak;
    if (score < 5) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }

  /// إنشاء كلمة مرور قوية
  static String generateStrongPassword({int length = 12}) {
    const String lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const String uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const String numbers = '0123456789';
    const String symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';

    final Random random = Random.secure();
    final StringBuffer password = StringBuffer();

    // ضمان وجود نوع واحد على الأقل من كل فئة
    password.write(lowercase[random.nextInt(lowercase.length)]);
    password.write(uppercase[random.nextInt(uppercase.length)]);
    password.write(numbers[random.nextInt(numbers.length)]);
    password.write(symbols[random.nextInt(symbols.length)]);

    // ملء باقي الطول
    const String allChars = lowercase + uppercase + numbers + symbols;
    for (int i = 4; i < length; i++) {
      password.write(allChars[random.nextInt(allChars.length)]);
    }

    // خلط الأحرف
    final List<String> chars = password.toString().split('');
    chars.shuffle(random);

    return chars.join('');
  }

  /// تسجيل محاولة دخول
  static Future<void> logLoginAttempt({
    required bool success,
    String? username,
    String? ipAddress,
  }) async {
    final Map<String, Object?> attempt = <String, Object?>{
      'timestamp': DateTime.now().toIso8601String(),
      'success': success,
      'username': username,
      'ip_address': ipAddress,
    };

    final List<Map<String, dynamic>> attempts =
        SettingsHelper.getLoginAttempts();
    attempts.add(attempt);

    // الاحتفاظ بآخر 100 محاولة فقط
    if (attempts.length > 100) {
      attempts.removeRange(0, attempts.length - 100);
    }

    await SettingsHelper.setLoginAttempts(attempts);
  }

  /// التحقق من محاولات الدخول المشبوهة
  static bool checkSuspiciousActivity() {
    final List<Map<String, dynamic>> attempts =
        SettingsHelper.getLoginAttempts();
    final DateTime now = DateTime.now();

    // التحقق من محاولات فاشلة متتالية في آخر 15 دقيقة
    final int recentFailures = attempts.where((Map<String, dynamic> attempt) {
      final DateTime timestamp = DateTime.parse(attempt['timestamp']);
      return !attempt['success'] && now.difference(timestamp).inMinutes <= 15;
    }).length;

    return recentFailures >= 5; // 5 محاولات فاشلة في 15 دقيقة
  }

  /// قفل الحساب مؤقتاً
  static Future<void> lockAccount({Duration? duration}) async {
    duration ??= const Duration(minutes: 30);
    final DateTime unlockTime = DateTime.now().add(duration);
    await SettingsHelper.setAccountLockTime(unlockTime);
  }

  /// التحقق من قفل الحساب
  static bool isAccountLocked() {
    final DateTime? lockTime = SettingsHelper.getAccountLockTime();
    if (lockTime == null) return false;

    return DateTime.now().isBefore(lockTime);
  }

  /// إلغاء قفل الحساب
  static Future<void> unlockAccount() async {
    await SettingsHelper.clearAccountLockTime();
  }

  /// تنظيف البيانات الحساسة من الذاكرة
  static void clearSensitiveData(String data) {
    // في Dart، لا يمكن مسح البيانات من الذاكرة مباشرة
    // لكن يمكن تجنب الاحتفاظ بمراجع للبيانات الحساسة
    if (kDebugMode) {
      // في وضع التطوير، يمكن طباعة تحذير
      debugPrint('تم مسح البيانات الحساسة');
    }
  }

  /// التحقق من سلامة البيانات
  static String calculateChecksum(String data) {
    final Uint8List bytes = utf8.encode(data);
    final Digest digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// التحقق من سلامة البيانات
  static bool verifyChecksum(String data, String expectedChecksum) {
    final String actualChecksum = calculateChecksum(data);
    return actualChecksum == expectedChecksum;
  }

  /// الحصول على معلومات الأمان
  static Map<String, dynamic> getSecurityInfo() {
    final List<Map<String, dynamic>> attempts =
        SettingsHelper.getLoginAttempts();
    final DateTime now = DateTime.now();

    final List<Map<String, dynamic>> recentAttempts =
        attempts.where((Map<String, dynamic> attempt) {
      final DateTime timestamp = DateTime.parse(attempt['timestamp']);
      return now.difference(timestamp).inDays <= 7;
    }).toList();

    final int successfulAttempts =
        recentAttempts.where((Map<String, dynamic> a) => a['success']).length;
    final int failedAttempts =
        recentAttempts.where((Map<String, dynamic> a) => !a['success']).length;

    return <String, dynamic>{
      'total_attempts': attempts.length,
      'recent_attempts': recentAttempts.length,
      'successful_attempts': successfulAttempts,
      'failed_attempts': failedAttempts,
      'account_locked': isAccountLocked(),
      'suspicious_activity': checkSuspiciousActivity(),
      'last_login': (() {
        final List<Map<String, dynamic>> successfulAttempts =
            attempts.where((Map<String, dynamic> a) => a['success']).toList();
        return successfulAttempts.isNotEmpty
            ? successfulAttempts.last['timestamp'] as String?
            : null;
      })(),
    };
  }
}

/// قوة كلمة المرور
enum PasswordStrength {
  weak,
  medium,
  strong,
}

/// امتداد لقوة كلمة المرور
extension PasswordStrengthExtension on PasswordStrength {
  String get displayName {
    switch (this) {
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.medium:
        return 'متوسطة';
      case PasswordStrength.strong:
        return 'قوية';
    }
  }

  String get description {
    switch (this) {
      case PasswordStrength.weak:
        return 'كلمة المرور ضعيفة وتحتاج تحسين';
      case PasswordStrength.medium:
        return 'كلمة المرور متوسطة القوة';
      case PasswordStrength.strong:
        return 'كلمة المرور قوية وآمنة';
    }
  }
}
