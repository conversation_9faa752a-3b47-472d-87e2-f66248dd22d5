/// Validation utilities for form inputs
class Validators {
  static var validateName;

  static var validatePhone;

  static var validateEmail;

  static var validateDescription;

  static var validateAddress;

  static var validateQuantity;

  static var validatePrice;

  /// Validate required fields
  static String? required(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validate email format
  static String? email(String? value) {
    if (value == null || value.isEmpty) return null;

    final RegExp emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  /// Validate phone number
  static String? phone(String? value) {
    if (value == null || value.isEmpty) return null;

    final RegExp phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  /// Validate positive numbers
  static String? positiveNumber(String? value, [String? fieldName]) {
    if (value == null || value.isEmpty) return null;

    final double? number = double.tryParse(value);
    if (number == null) {
      return '${fieldName ?? 'This field'} must be a valid number';
    }
    if (number <= 0) {
      return '${fieldName ?? 'This field'} must be greater than 0';
    }
    return null;
  }

  /// Validate non-negative numbers
  static String? nonNegativeNumber(String? value, [String? fieldName]) {
    if (value == null || value.isEmpty) return null;

    final double? number = double.tryParse(value);
    if (number == null) {
      return '${fieldName ?? 'This field'} must be a valid number';
    }
    if (number < 0) {
      return '${fieldName ?? 'This field'} cannot be negative';
    }
    return null;
  }

  /// Validate minimum length
  static String? minLength(String? value, int minLength, [String? fieldName]) {
    if (value == null || value.isEmpty) return null;

    if (value.length < minLength) {
      return '${fieldName ?? 'This field'} must be at least $minLength characters';
    }
    return null;
  }

  /// Validate maximum length
  static String? maxLength(String? value, int maxLength, [String? fieldName]) {
    if (value == null || value.isEmpty) return null;

    if (value.length > maxLength) {
      return '${fieldName ?? 'This field'} cannot exceed $maxLength characters';
    }
    return null;
  }

  /// Combine multiple validators
  static String? combine(
      String? value, List<String? Function(String?)> validators) {
    for (final String? Function(String? p1) validator in validators) {
      final String? result = validator(value);
      if (result != null) return result;
    }
    return null;
  }

  static validateNumber(String? value,
      {required String fieldName,
      required bool isRequired,
      required int min}) {}

  static validateDropdown(String? value, {required String fieldName}) {}

  /// التحقق من صحة البريد الإلكتروني (طريقة بسيطة)
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;

    final RegExp emailRegex =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

    return emailRegex.hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف السعودي (طريقة بسيطة)
  static bool isValidPhone(String phone) {
    if (phone.isEmpty) return false;

    // إزالة المسافات والرموز
    final String cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // أنماط أرقام الهاتف السعودية المقبولة
    final List<RegExp> phonePatterns = <RegExp>[
      RegExp(r'^05[0-9]{8}$'), // 05xxxxxxxx
      RegExp(r'^9665[0-9]{8}$'), // 9665xxxxxxxx
      RegExp(r'^\+9665[0-9]{8}$'), // +9665xxxxxxxx
      RegExp(r'^01[0-9]{7}$'), // أرقام أرضية
    ];

    return phonePatterns.any((RegExp pattern) => pattern.hasMatch(cleanPhone));
  }

  /// التحقق من صحة السعر (طريقة بسيطة)
  static bool isValidPrice(String price) {
    if (price.isEmpty) return false;

    final double? parsedPrice = double.tryParse(price);

    return parsedPrice != null && parsedPrice >= 0;
  }

  /// التحقق من صحة الكمية (طريقة بسيطة)
  static bool isValidQuantity(String quantity) {
    if (quantity.isEmpty) return false;

    final int? parsedQuantity = int.tryParse(quantity);

    return parsedQuantity != null && parsedQuantity >= 0;
  }
}
