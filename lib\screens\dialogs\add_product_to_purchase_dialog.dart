import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/product.dart';
import '../../models/purchase_item.dart';
import '../../providers/product_provider.dart';
import '../../config/app_colors.dart';
import '../../config/app_dimensions.dart';
import '../../config/app_styles.dart';
import '../../utils/validators.dart';

/// حوار إضافة منتج لفاتورة الشراء
class AddProductToPurchaseDialog extends StatefulWidget {
  final Function(PurchaseItem) onProductAdded;
  final PurchaseItem? existingItem; // للتعديل

  const AddProductToPurchaseDialog({
    super.key,
    required this.onProductAdded,
    this.existingItem,
  });

  @override
  State<AddProductToPurchaseDialog> createState() => _AddProductToPurchaseDialogState();
}

class _AddProductToPurchaseDialogState extends State<AddProductToPurchaseDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  final TextEditingController _unitPriceController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  List<Product> _filteredProducts = <Product>[];
  Product? _selectedProduct;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadProducts();
    _initializeForEdit();
  }

  void _initializeForEdit() {
    if (widget.existingItem != null) {
      final PurchaseItem item = widget.existingItem!;
      _quantityController.text = item.quantity?.toString() ?? '';
      _unitPriceController.text = item.unitPrice?.toString() ?? '';
      _notesController.text = item.notes ?? '';
      
      // البحث عن المنتج المحدد
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final ProductProvider productProvider = context.read<ProductProvider>();
        _selectedProduct = productProvider.products.firstWhere(
          (Product p) => p.id == item.productId,
          orElse: () => Product(name: item.productName ?? '', price: 0),
        );
        setState(() {});
      });
    }
  }

  void _loadProducts() {
    final ProductProvider productProvider = context.read<ProductProvider>();
    productProvider.fetchProducts();
    _filterProducts('');
  }

  void _filterProducts(String query) {
    final ProductProvider productProvider = context.read<ProductProvider>();
    final List<Product> allProducts = productProvider.products;

    setState(() {
      if (query.isEmpty) {
        _filteredProducts = allProducts;
      } else {
        _filteredProducts = allProducts.where((Product product) {
          return product.name.toLowerCase().contains(query.toLowerCase()) ||
              (product.barcode?.toLowerCase().contains(query.toLowerCase()) ?? false);
        }).toList();
      }
    });
  }

  void _calculateTotal() {
    final double quantity = double.tryParse(_quantityController.text) ?? 0;
    final double unitPrice = double.tryParse(_unitPriceController.text) ?? 0;
    // يمكن إضافة عرض المجموع هنا
  }

  void _addProduct() {
    if (!_formKey.currentState!.validate() || _selectedProduct == null) {
      return;
    }

    final double quantity = double.tryParse(_quantityController.text) ?? 0;
    final double unitPrice = double.tryParse(_unitPriceController.text) ?? 0;
    final double totalPrice = quantity * unitPrice;

    final PurchaseItem purchaseItem = PurchaseItem(
      id: widget.existingItem?.id,
      purchaseId: widget.existingItem?.purchaseId,
      productId: _selectedProduct!.id,
      productName: _selectedProduct!.name,
      quantity: quantity,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
    );

    widget.onProductAdded(purchaseItem);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            children: <Widget>[
              _buildHeader(),
              const SizedBox(height: AppDimensions.paddingM),
              Expanded(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: <Widget>[
                      _buildProductSelection(),
                      const SizedBox(height: AppDimensions.paddingM),
                      if (_selectedProduct != null) ...[
                        _buildSelectedProductInfo(),
                        const SizedBox(height: AppDimensions.paddingM),
                        _buildQuantityAndPrice(),
                        const SizedBox(height: AppDimensions.paddingM),
                        _buildNotesField(),
                        const Spacer(),
                        _buildTotal(),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppDimensions.paddingM),
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: <Widget>[
        const Icon(
          Icons.add_shopping_cart,
          color: AppColors.primary,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.paddingS),
        Expanded(
          child: Text(
            widget.existingItem != null ? 'تعديل المنتج' : 'إضافة منتج للفاتورة',
            style: AppStyles.titleLarge,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildProductSelection() {
    if (_selectedProduct != null) {
      return Card(
        child: ListTile(
          leading: const Icon(Icons.inventory_2, color: AppColors.primary),
          title: Text(_selectedProduct!.name),
          subtitle: Text('سعر الشراء: ${_selectedProduct!.purchasePrice?.toStringAsFixed(2) ?? _selectedProduct!.price.toStringAsFixed(2)} ر.س'),
          trailing: IconButton(
            icon: const Icon(Icons.change_circle),
            onPressed: () {
              setState(() {
                _selectedProduct = null;
              });
            },
          ),
        ),
      );
    }

    return Column(
      children: <Widget>[
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'البحث عن منتج',
            hintText: 'ادخل اسم المنتج أو الباركود',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onChanged: _filterProducts,
        ),
        const SizedBox(height: AppDimensions.paddingS),
        SizedBox(
          height: 200,
          child: _buildProductsList(),
        ),
      ],
    );
  }

  Widget _buildProductsList() {
    return Consumer<ProductProvider>(
      builder: (BuildContext context, ProductProvider provider, Widget? child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_filteredProducts.isEmpty) {
          return const Center(
            child: Text('لا توجد منتجات'),
          );
        }

        return ListView.builder(
          itemCount: _filteredProducts.length,
          itemBuilder: (BuildContext context, int index) {
            final Product product = _filteredProducts[index];
            return Card(
              child: ListTile(
                title: Text(product.name),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text('سعر الشراء: ${product.purchasePrice?.toStringAsFixed(2) ?? product.price.toStringAsFixed(2)} ر.س'),
                    Text('المخزن: ${product.warehouseQuantity ?? 0}'),
                  ],
                ),
                onTap: () {
                  setState(() {
                    _selectedProduct = product;
                    _unitPriceController.text = (product.purchasePrice ?? product.price).toString();
                  });
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSelectedProductInfo() {
    return Card(
      color: AppColors.primary.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'المنتج المحدد:',
              style: AppStyles.titleMedium.copyWith(color: AppColors.primary),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text('الاسم: ${_selectedProduct!.name}'),
            Text('الوحدة: ${_selectedProduct!.unit ?? 'قطعة'}'),
            Text('المتوفر في المخزن: ${_selectedProduct!.warehouseQuantity ?? 0}'),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityAndPrice() {
    return Row(
      children: <Widget>[
        Expanded(
          child: TextFormField(
            controller: _quantityController,
            decoration: const InputDecoration(
              labelText: 'الكمية *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.numbers),
            ),
            keyboardType: TextInputType.number,
            validator: Validators.validateQuantity,
            onChanged: (_) => _calculateTotal(),
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: TextFormField(
            controller: _unitPriceController,
            decoration: const InputDecoration(
              labelText: 'سعر الوحدة *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.attach_money),
              suffixText: 'ر.س',
            ),
            keyboardType: TextInputType.number,
            validator: Validators.validatePrice,
            onChanged: (_) => _calculateTotal(),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'ملاحظات (اختياري)',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.note),
      ),
      maxLines: 2,
    );
  }

  Widget _buildTotal() {
    final double quantity = double.tryParse(_quantityController.text) ?? 0;
    final double unitPrice = double.tryParse(_unitPriceController.text) ?? 0;
    final double total = quantity * unitPrice;

    return Card(
      color: AppColors.success.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(
              'المجموع:',
              style: AppStyles.titleMedium,
            ),
            Text(
              '${total.toStringAsFixed(2)} ر.س',
              style: AppStyles.titleLarge.copyWith(
                color: AppColors.success,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      children: <Widget>[
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: ElevatedButton(
            onPressed: _selectedProduct != null ? _addProduct : null,
            child: Text(widget.existingItem != null ? 'تحديث' : 'إضافة'),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
