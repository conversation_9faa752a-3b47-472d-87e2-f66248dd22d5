/// اختبارات مخططات قاعدة البيانات - أسامة ماركت
///
/// تتحقق من توافق مخططات الجداول مع النماذج
library database_schema_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  // تهيئة بيانات اللغة وقاعدة البيانات للاختبار
  setUpAll(() async {
    await initializeDateFormatting('ar', null);
    await initializeDateFormatting('ar_SA', null);
    
    // تهيئة databaseFactory للاختبارات
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
    
    // تفعيل وضع الاختبار مع قاعدة بيانات في الذاكرة
    DatabaseService.enableTestMode();
    await DatabaseService.instance.database;
  });

  // تنظيف قاعدة البيانات بعد كل اختبار
  tearDown(() async {
    await DatabaseService.instance.clearDatabaseForTesting();
  });

  // إغلاق قاعدة البيانات بعد جميع الاختبارات
  tearDownAll(() async {
    DatabaseService.disableTestMode();
    await DatabaseService.instance.resetDatabase();
  });

  group('اختبارات توافق مخططات الجداول مع النماذج', () {
    
    test('يجب أن يدعم جدول المنتجات جميع حقول نموذج Product', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إنشاء منتج كامل مع جميع الحقول
      final Product testProduct = Product(
        name: 'منتج اختبار مخطط',
        price: 25.0,
        barcode: '1234567890123',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 200,
        storeQuantity: 100,
        retailPrice: 35.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // إدراج المنتج باستخدام toMap
      final int productId = await database.insert('products', testProduct.toMap());
      expect(productId, greaterThan(0));
      
      // استرجاع المنتج والتحقق من جميع الحقول
      final List<Map<String, dynamic>> retrievedProducts = 
          await database.query('products', where: 'id = ?', whereArgs: <Object?>[productId]);
      expect(retrievedProducts.length, equals(1));
      
      final Product retrievedProduct = Product.fromMap(retrievedProducts.first);
      expect(retrievedProduct.name, equals('منتج اختبار مخطط'));
      expect(retrievedProduct.price, equals(25.0));
      expect(retrievedProduct.barcode, equals('1234567890123'));
      expect(retrievedProduct.category, equals('فئة اختبار'));
      expect(retrievedProduct.unit, equals('قطعة'));
      expect(retrievedProduct.warehouseQuantity, equals(200));
      expect(retrievedProduct.storeQuantity, equals(100));
      expect(retrievedProduct.retailPrice, equals(35.0));
      expect(retrievedProduct.createdAt, isNotNull);
      expect(retrievedProduct.updatedAt, isNotNull);
    });

    test('يجب أن يدعم جدول العملاء جميع حقول نموذج Customer', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إنشاء عميل كامل مع جميع الحقول
      final Customer testCustomer = Customer(
        name: 'عميل اختبار مخطط',
        phone: '0501234567',
        email: '<EMAIL>',
        address: 'عنوان اختبار مفصل',
        balance: 150.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        notes: 'عميل مميز للاختبار',
      );
      
      // إدراج العميل باستخدام toMap
      final int customerId = await database.insert('customers', testCustomer.toMap());
      expect(customerId, greaterThan(0));
      
      // استرجاع العميل والتحقق من جميع الحقول
      final List<Map<String, dynamic>> retrievedCustomers = 
          await database.query('customers', where: 'id = ?', whereArgs: <Object?>[customerId]);
      expect(retrievedCustomers.length, equals(1));
      
      final Customer retrievedCustomer = Customer.fromMap(retrievedCustomers.first);
      expect(retrievedCustomer.name, equals('عميل اختبار مخطط'));
      expect(retrievedCustomer.phone, equals('0501234567'));
      expect(retrievedCustomer.email, equals('<EMAIL>'));
      expect(retrievedCustomer.address, equals('عنوان اختبار مفصل'));
      expect(retrievedCustomer.balance, equals(150.0));
      expect(retrievedCustomer.notes, equals('عميل مميز للاختبار'));
      expect(retrievedCustomer.createdAt, isNotNull);
      expect(retrievedCustomer.updatedAt, isNotNull);
    });

    test('يجب أن يدعم جدول المبيعات جميع حقول نموذج Sale', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إنشاء بيع كامل مع جميع الحقول
      final Sale testSale = Sale(
        customerId: 1,
        customerName: 'عميل اختبار',
        total: 250.0,
        paidAmount: 200.0,
        remainingAmount: 50.0,
        saleType: 'retail',
        paymentMethod: 'cash',
        status: 'completed',
        invoiceNumber: 'INV-001',
        date: DateTime.now().toIso8601String(),
        notes: 'بيع اختبار مخطط',
        totalWholesaleAmount: 100.0,
        totalRetailAmount: 150.0,
        remainingRetailAmount: 25.0,
        notesForRetailItems: 'ملاحظات بنود التجزئة',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // إدراج البيع باستخدام toMap
      final int saleId = await database.insert('sales', testSale.toMap());
      expect(saleId, greaterThan(0));
      
      // استرجاع البيع والتحقق من جميع الحقول
      final List<Map<String, dynamic>> retrievedSales = 
          await database.query('sales', where: 'id = ?', whereArgs: <Object?>[saleId]);
      expect(retrievedSales.length, equals(1));
      
      final Sale retrievedSale = Sale.fromMap(retrievedSales.first);
      expect(retrievedSale.customerId, equals(1));
      expect(retrievedSale.customerName, equals('عميل اختبار'));
      expect(retrievedSale.total, equals(250.0));
      expect(retrievedSale.paidAmount, equals(200.0));
      expect(retrievedSale.remainingAmount, equals(50.0));
      expect(retrievedSale.saleType, equals('retail'));
      expect(retrievedSale.paymentMethod, equals('cash'));
      expect(retrievedSale.status, equals('completed'));
      expect(retrievedSale.invoiceNumber, equals('INV-001'));
      expect(retrievedSale.notes, equals('بيع اختبار مخطط'));
      expect(retrievedSale.totalWholesaleAmount, equals(100.0));
      expect(retrievedSale.totalRetailAmount, equals(150.0));
      expect(retrievedSale.remainingRetailAmount, equals(25.0));
      expect(retrievedSale.notesForRetailItems, equals('ملاحظات بنود التجزئة'));
      expect(retrievedSale.createdAt, isNotNull);
      expect(retrievedSale.updatedAt, isNotNull);
    });

    test('يجب أن تعمل طرق copyWith مع قاعدة البيانات', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إنشاء منتج أصلي
      final Product originalProduct = Product(
        name: 'منتج للتحديث',
        price: 20.0,
        category: 'فئة أصلية',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        retailPrice: 30.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final int productId = await database.insert('products', originalProduct.toMap());
      
      // تحديث المنتج باستخدام copyWith
      final Product updatedProduct = originalProduct.copyWith(
        id: productId,
        name: 'منتج محدث',
        price: 25.0,
        category: 'فئة محدثة',
        updatedAt: DateTime.now(),
      );
      
      // حفظ التحديث في قاعدة البيانات
      final int updatedRows = await database.update(
        'products', 
        updatedProduct.toMap(),
        where: 'id = ?',
        whereArgs: <Object?>[productId],
      );
      expect(updatedRows, equals(1));
      
      // التحقق من التحديث
      final List<Map<String, dynamic>> retrievedProducts = 
          await database.query('products', where: 'id = ?', whereArgs: <Object?>[productId]);
      final Product retrievedProduct = Product.fromMap(retrievedProducts.first);
      
      expect(retrievedProduct.name, equals('منتج محدث'));
      expect(retrievedProduct.price, equals(25.0));
      expect(retrievedProduct.category, equals('فئة محدثة'));
      // الحقول غير المحدثة يجب أن تبقى كما هي
      expect(retrievedProduct.unit, equals('قطعة'));
      expect(retrievedProduct.warehouseQuantity, equals(100));
      expect(retrievedProduct.storeQuantity, equals(50));
      expect(retrievedProduct.retailPrice, equals(30.0));
    });

    test('يجب أن تتعامل مع القيم الافتراضية بشكل صحيح', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إدراج منتج بحقول أساسية فقط
      final Map<String, dynamic> minimalProduct = <String, dynamic>{
        'name': 'منتج بسيط',
        'price': 15.0,
      };
      
      final int productId = await database.insert('products', minimalProduct);
      expect(productId, greaterThan(0));
      
      // التحقق من القيم الافتراضية
      final List<Map<String, dynamic>> retrievedProducts = 
          await database.query('products', where: 'id = ?', whereArgs: <Object?>[productId]);
      final Map<String, dynamic> product = retrievedProducts.first;
      
      expect(product['warehouseQuantity'], equals(0)); // DEFAULT 0
      expect(product['storeQuantity'], equals(0)); // DEFAULT 0
      expect(product['description'], equals('')); // DEFAULT ''
      
      // إدراج عميل بحقول أساسية فقط
      final Map<String, dynamic> minimalCustomer = <String, dynamic>{
        'name': 'عميل بسيط',
      };
      
      final int customerId = await database.insert('customers', minimalCustomer);
      expect(customerId, greaterThan(0));
      
      // التحقق من القيم الافتراضية
      final List<Map<String, dynamic>> retrievedCustomers = 
          await database.query('customers', where: 'id = ?', whereArgs: <Object?>[customerId]);
      final Map<String, dynamic> customer = retrievedCustomers.first;
      
      expect(customer['balance'], equals(0.0)); // DEFAULT 0.0
    });

    test('يجب أن تدعم الاستعلامات المعقدة مع الحقول الجديدة', () async {
      final Database database = await DatabaseService.instance.database;
      
      // إضافة منتجات متعددة
      final List<Product> products = <Product>[
        Product(
          name: 'منتج أ',
          price: 10.0,
          category: 'فئة 1',
          warehouseQuantity: 100,
          storeQuantity: 50,
          retailPrice: 15.0,
          barcode: '1111111111111',
          createdAt: DateTime.now(),
        ),
        Product(
          name: 'منتج ب',
          price: 20.0,
          category: 'فئة 2',
          warehouseQuantity: 200,
          storeQuantity: 75,
          retailPrice: 30.0,
          barcode: '2222222222222',
          createdAt: DateTime.now(),
        ),
        Product(
          name: 'منتج ج',
          price: 15.0,
          category: 'فئة 1',
          warehouseQuantity: 150,
          storeQuantity: 25,
          retailPrice: 25.0,
          barcode: '3333333333333',
          createdAt: DateTime.now(),
        ),
      ];
      
      for (final Product product in products) {
        await database.insert('products', product.toMap());
      }
      
      // استعلام للمنتجات في فئة معينة
      final List<Map<String, dynamic>> category1Products = await database.query(
        'products',
        where: 'category = ?',
        whereArgs: <Object?>['فئة 1'],
        orderBy: 'price ASC',
      );
      
      expect(category1Products.length, equals(2));
      expect(category1Products.first['name'], equals('منتج أ'));
      expect(category1Products.last['name'], equals('منتج ج'));
      
      // استعلام للمنتجات بكمية مخزن أكبر من 100
      final List<Map<String, dynamic>> highStockProducts = await database.query(
        'products',
        where: 'warehouseQuantity > ?',
        whereArgs: <Object?>[100],
        orderBy: 'warehouseQuantity DESC',
      );
      
      expect(highStockProducts.length, equals(2));
      expect(highStockProducts.first['name'], equals('منتج ب'));
      expect(highStockProducts.last['name'], equals('منتج ج'));
      
      // استعلام للمنتجات بباركود محدد
      final List<Map<String, dynamic>> barcodeProducts = await database.query(
        'products',
        where: 'barcode = ?',
        whereArgs: <Object?>['2222222222222'],
      );
      
      expect(barcodeProducts.length, equals(1));
      expect(barcodeProducts.first['name'], equals('منتج ب'));
    });
  });
}
