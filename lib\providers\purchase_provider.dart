import 'package:flutter/material.dart';
import '../models/purchase.dart';
import '../models/purchase_item.dart';
import '../services/purchase_service.dart';

/// Provider for managing purchase operations
class PurchaseProvider extends ChangeNotifier {
  final PurchaseService _purchaseService;
  // Transaction functionality integrated directly

  List<Purchase> _purchases = <Purchase>[];
  List<Purchase> _filteredPurchases = <Purchase>[];
  bool _isLoading = false;
  String? _error;

  /// منشئ افتراضي (للتوافق مع الكود الحالي)
  PurchaseProvider() : _purchaseService = PurchaseService();

  /// منشئ مع حقن الاعتماديات
  PurchaseProvider.withService(this._purchaseService);

  /// Get the list of purchases
  List<Purchase> get purchases => _purchases;

  /// Get the filtered list of purchases
  List<Purchase> get filteredPurchases =>
      _filteredPurchases.isEmpty ? _purchases : _filteredPurchases;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Fetch all purchases from the database
  Future<void> fetchPurchases() async {
    _setLoading(true);
    _clearError();

    try {
      _purchases = await _purchaseService.getAllPurchases();
      notifyListeners();
    } catch (e) {
      _setError('Failed to fetch purchases: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await fetchPurchases();
  }

  /// Load purchases (alias for fetchPurchases)
  Future<void> loadPurchases() async {
    await fetchPurchases();
  }

  /// Filter purchases based on search query, status, and date range
  void filterPurchases({
    String? searchQuery,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    List<Purchase> filtered = List.from(_purchases);

    // Filter by search query
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filtered = filtered.where((Purchase purchase) {
        return purchase.id.toString().contains(searchQuery) ||
            (purchase.notes
                    ?.toLowerCase()
                    .contains(searchQuery.toLowerCase()) ??
                false);
      }).toList();
    }

    // Filter by status
    if (status != null && status.isNotEmpty) {
      filtered = filtered
          .where((Purchase purchase) => purchase.status == status)
          .toList();
    }

    // Filter by date range
    if (startDate != null && endDate != null) {
      filtered = filtered.where((Purchase purchase) {
        if (purchase.date == null) return false;
        final DateTime purchaseDate = DateTime.parse(purchase.date!);
        return purchaseDate
                .isAfter(startDate.subtract(const Duration(days: 1))) &&
            purchaseDate.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    }

    _filteredPurchases = filtered;
    notifyListeners();
  }

  /// Add a new purchase with items
  Future<Purchase> addPurchase(
      Purchase purchase, List<PurchaseItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // Create purchase with items using transaction service
      // TODO: Implement createPurchaseWithItems in TransactionService
      await _purchaseService.insertPurchase(purchase);

      // Reload purchases
      await fetchPurchases();

      // Return the added purchase
      final Purchase addedPurchase =
          _purchases.isNotEmpty ? _purchases.first : purchase;
      return addedPurchase;
    } catch (e) {
      _setError('Failed to add purchase: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing purchase with items
  Future<Purchase> updatePurchase(
      Purchase purchase, List<PurchaseItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      // Update purchase with items using transaction service
      // TODO: Implement updatePurchaseWithItems in TransactionService
      await _purchaseService.updatePurchase(purchase);

      // Reload purchases
      await fetchPurchases();

      // Return the updated purchase
      final Purchase updatedPurchase =
          _purchases.where((Purchase p) => p.id == purchase.id).firstOrNull ??
              purchase;
      return updatedPurchase;
    } catch (e) {
      _setError('Failed to update purchase: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update purchase status only
  Future<void> updatePurchaseStatus(Purchase purchase) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseService.updatePurchase(purchase);
      await fetchPurchases();
    } catch (e) {
      _setError('Failed to update purchase status: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a purchase
  Future<void> deletePurchase(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _purchaseService.deletePurchase(id);
      await fetchPurchases();
    } catch (e) {
      _setError('Failed to delete purchase: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get purchases by supplier
  Future<void> getPurchasesBySupplier(int supplierId) async {
    _setLoading(true);
    _clearError();

    try {
      _purchases = await _purchaseService.getPurchasesBySupplier(supplierId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get purchases by supplier: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get purchases by date range
  Future<void> getPurchasesByDateRange(String startDate, String endDate) async {
    _setLoading(true);
    _clearError();

    try {
      _purchases =
          await _purchaseService.getPurchasesByDateRange(startDate, endDate);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get purchases by date range: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get purchase items for a specific purchase
  Future<List<PurchaseItem>> getPurchaseItems(int purchaseId) async {
    try {
      return await _purchaseService.getPurchaseItems(purchaseId);
    } catch (e) {
      _setError('Failed to get purchase items: $e');
      return <PurchaseItem>[];
    }
  }

  /// Get total purchases amount
  Future<double> getTotalPurchasesAmount() async {
    try {
      return await _purchaseService.getTotalPurchasesAmount();
    } catch (e) {
      _setError('Failed to get total purchases amount: $e');
      return 0.0;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Generate purchase invoice number
  String generatePurchaseInvoiceNumber() {
    final DateTime now = DateTime.now();
    final int count = _purchases.length + 1;
    return 'PUR-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${count.toString().padLeft(4, '0')}';
  }

  /// Get total purchases amount
  double get totalPurchasesAmount {
    return _purchases.fold(
        0.0, (double sum, Purchase purchase) => sum + (purchase.total ?? 0));
  }

  /// Get total purchases count
  int get totalPurchasesCount => _purchases.length;

  /// Get completed purchases count
  int get completedPurchasesCount {
    return _purchases
        .where((Purchase purchase) => purchase.status == 'completed')
        .length;
  }

  /// Get pending purchases count
  int get pendingPurchasesCount {
    return _purchases
        .where((Purchase purchase) => purchase.status == 'pending')
        .length;
  }

  /// Search purchases
  List<Purchase> searchPurchases(String query) {
    if (query.isEmpty) return _purchases;

    return _purchases.where((Purchase purchase) {
      return purchase.id.toString().contains(query) ||
          (purchase.notes?.toLowerCase().contains(query.toLowerCase()) ??
              false);
    }).toList();
  }

  /// Clear all purchases
  void clearPurchases() {
    _purchases.clear();
    notifyListeners();
  }
}
