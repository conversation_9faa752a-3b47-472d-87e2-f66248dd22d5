/// اختبارات الخدمات (Service Tests) لتطبيق أسامة ماركت
///
/// تتضمن اختبارات لجميع الخدمات والعمليات الأساسية
/// للتأكد من عمل طبقة البيانات بشكل صحيح
library service_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:inventory_management_app/services/database_service.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/customer_service.dart';
import 'package:inventory_management_app/services/sale_service.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/models/sale.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  // إعداد قاعدة البيانات واللغة للاختبار قبل جميع الاختبارات
  setUpAll(() async {
    // تهيئة sqflite_ffi للاختبارات
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;

    // تهيئة بيانات اللغة العربية
    await initializeDateFormatting('ar', null);
    await initializeDateFormatting('ar_SA', null);

    // تفعيل وضع الاختبار مع قاعدة بيانات في الذاكرة
    DatabaseService.enableTestMode();

    // تهيئة قاعدة البيانات
    await DatabaseService.instance.database;
  });

  // تنظيف قاعدة البيانات بعد كل اختبار
  tearDown(() async {
    await DatabaseService.instance.clearDatabaseForTesting();
  });

  // إلغاء تفعيل وضع الاختبار بعد جميع الاختبارات
  tearDownAll(() async {
    DatabaseService.disableTestMode();
  });

  group('اختبارات خدمة قاعدة البيانات (DatabaseService)', () {
    test('يجب أن تتصل بقاعدة البيانات بنجاح', () async {
      final Database database = await DatabaseService.instance.database;
      expect(database, isNotNull);
      expect(database.isOpen, isTrue);
    });

    test('يجب أن تنشئ الجداول بشكل صحيح', () async {
      final Database database = await DatabaseService.instance.database;

      // التحقق من وجود جدول المنتجات
      final List<Map<String, dynamic>> tables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='products'");
      expect(tables.isNotEmpty, isTrue);

      // التحقق من وجود جدول العملاء
      final List<Map<String, dynamic>> customerTables = await database.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='customers'");
      expect(customerTables.isNotEmpty, isTrue);
    });
  });

  group('اختبارات خدمة المنتجات (ProductService)', () {
    late ProductService productService;

    setUp(() {
      productService = ProductService();
    });

    test('يجب أن تضيف منتج جديد بنجاح', () async {
      final Product testProduct = Product(
        name: 'منتج خدمة اختبار',
        price: 10.0, // السعر الأساسي (الجملة)
        barcode: '111222333',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 200,
        storeQuantity: 100,
        wholesalePrice: 20.0,
        retailPrice: 30.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int productId = await productService.addProduct(testProduct);
      expect(productId, greaterThan(0));
    });

    test('يجب أن تسترجع جميع المنتجات بنجاح', () async {
      final List<Product> products = await productService.getAllProducts();
      expect(products, isA<List<Product>>());
    });

    test('يجب أن تبحث عن منتج بالباركود', () async {
      // إضافة منتج للبحث عنه
      final Product testProduct = Product(
        name: 'منتج للبحث',
        price: 10.0, // السعر الأساسي (الجملة)
        barcode: '999888777',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 50,
        storeQuantity: 25,
        wholesalePrice: 15.0,
        retailPrice: 25.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await productService.addProduct(testProduct);

      // البحث عن المنتج
      final Product? foundProduct =
          await productService.getProductByBarcode('999888777');
      expect(foundProduct, isNotNull);
      expect(foundProduct!.name, equals('منتج للبحث'));
    });

    test('يجب أن تحدث كمية المنتج بنجاح', () async {
      // إضافة منتج للتحديث
      final Product testProduct = Product(
        name: 'منتج للتحديث',
        price: 15.0, // السعر الأساسي
        barcode: '555666777',
        category: 'فئة اختبار',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        wholesalePrice: 10.0,
        retailPrice: 20.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int productId = await productService.addProduct(testProduct);

      // تحديث الكمية
      await productService.updateProductQuantity(productId, 150, 75);

      // التحقق من التحديث
      final Product? updatedProduct =
          await productService.getProductById(productId);
      expect(updatedProduct, isNotNull);
      expect(updatedProduct!.warehouseQuantity, equals(150));
      expect(updatedProduct.storeQuantity, equals(75));
    });
  });

  group('اختبارات خدمة العملاء (CustomerService)', () {
    late CustomerService customerService;

    setUp(() {
      customerService = CustomerService();
    });

    test('يجب أن تضيف عميل جديد بنجاح', () async {
      final Customer testCustomer = Customer(
        name: 'عميل خدمة اختبار',
        phone: '0509876543',
        address: 'عنوان خدمة اختبار',
        balance: 500.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int customerId = await customerService.addCustomer(testCustomer);
      expect(customerId, greaterThan(0));
    });

    test('يجب أن تسترجع جميع العملاء بنجاح', () async {
      final List<Customer> customers = await customerService.getAllCustomers();
      expect(customers, isA<List<Customer>>());
    });

    test('يجب أن تبحث عن عميل بالهاتف', () async {
      // إضافة عميل للبحث عنه
      final Customer testCustomer = Customer(
        name: 'عميل للبحث',
        phone: '0512345678',
        address: 'عنوان للبحث',
        balance: 100.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await customerService.addCustomer(testCustomer);

      // البحث عن العميل
      final Customer? foundCustomer =
          await customerService.getCustomerByPhone('0512345678');
      expect(foundCustomer, isNotNull);
      expect(foundCustomer!.name, equals('عميل للبحث'));
    });

    test('يجب أن تحدث رصيد العميل بنجاح', () async {
      // إضافة عميل للتحديث
      final Customer testCustomer = Customer(
        name: 'عميل للتحديث',
        phone: '0587654321',
        address: 'عنوان للتحديث',
        balance: 200.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int customerId = await customerService.addCustomer(testCustomer);

      // تحديث الرصيد
      await customerService.updateCustomerBalance(customerId, 350.0);

      // التحقق من التحديث
      final Customer? updatedCustomer =
          await customerService.getCustomerById(customerId);
      expect(updatedCustomer, isNotNull);
      expect(updatedCustomer!.balance, equals(350.0));
    });
  });

  group('اختبارات خدمة المبيعات (SaleService)', () {
    late SaleService saleService;

    setUp(() {
      saleService = SaleService();
    });

    test('يجب أن تضيف بيع جديد بنجاح', () async {
      final Sale testSale = Sale(
        customerId: 1,
        customerName: 'عميل اختبار',
        totalAmount: 300.0,
        paidAmount: 250.0,
        remainingAmount: 50.0,
        saleType: 'retail',
        paymentMethod: 'cash',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int saleId = await saleService.addSale(testSale);
      expect(saleId, greaterThan(0));
    });

    test('يجب أن تسترجع جميع المبيعات بنجاح', () async {
      final List<Sale> sales = await saleService.getAllSales();
      expect(sales, isA<List<Sale>>());
    });

    test('يجب أن تسترجع مبيعات عميل محدد', () async {
      // إضافة بيع لعميل محدد
      final Sale testSale = Sale(
        customerId: 999,
        customerName: 'عميل محدد',
        totalAmount: 150.0,
        paidAmount: 150.0,
        remainingAmount: 0.0,
        saleType: 'wholesale',
        paymentMethod: 'credit',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await saleService.addSale(testSale);

      // استرجاع مبيعات العميل
      final List<Sale> customerSales =
          await saleService.getSalesByCustomer(999);
      expect(customerSales.isNotEmpty, isTrue);
      expect(customerSales.first.customerId, equals(999));
    });

    test('يجب أن تحسب إجمالي المبيعات لفترة محددة', () async {
      final DateTime startDate =
          DateTime.now().subtract(const Duration(days: 7));
      final DateTime endDate = DateTime.now();

      final double totalSales =
          await saleService.getTotalSalesForPeriod(startDate, endDate);
      expect(totalSales, greaterThanOrEqualTo(0.0));
    });
  });

  group('اختبارات التكامل بين الخدمات', () {
    test('يجب أن تعمل عملية بيع كاملة بنجاح', () async {
      final ProductService productService = ProductService();
      final CustomerService customerService = CustomerService();
      final SaleService saleService = SaleService();

      // إضافة منتج
      final Product testProduct = Product(
        name: 'منتج للبيع',
        price: 12.0, // السعر الأساسي
        barcode: '123123123',
        category: 'فئة البيع',
        unit: 'قطعة',
        warehouseQuantity: 100,
        storeQuantity: 50,
        wholesalePrice: 10.0,
        retailPrice: 15.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int productId = await productService.addProduct(testProduct);

      // إضافة عميل
      final Customer testCustomer = Customer(
        name: 'عميل للبيع',
        phone: '0555123456',
        address: 'عنوان للبيع',
        balance: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int customerId = await customerService.addCustomer(testCustomer);

      // إنشاء بيع
      final Sale testSale = Sale(
        customerId: customerId,
        customerName: 'عميل للبيع',
        totalAmount: 75.0, // 5 قطع × 15 ريال
        paidAmount: 50.0,
        remainingAmount: 25.0,
        saleType: 'retail',
        paymentMethod: 'cash',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final int saleId = await saleService.addSale(testSale);

      // تحديث كمية المنتج (تقليل 5 قطع من المتجر)
      await productService.updateProductQuantity(productId, 100, 45);

      // تحديث رصيد العميل (إضافة الدين)
      await customerService.updateCustomerBalance(customerId, 25.0);

      // التحقق من نجاح العملية
      expect(saleId, greaterThan(0));

      final Product? updatedProduct =
          await productService.getProductById(productId);
      expect(updatedProduct!.storeQuantity, equals(45));

      final Customer? updatedCustomer =
          await customerService.getCustomerById(customerId);
      expect(updatedCustomer!.balance, equals(25.0));
    });
  });
}
