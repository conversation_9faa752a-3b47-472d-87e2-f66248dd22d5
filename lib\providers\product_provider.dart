import 'package:flutter/material.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/services/product_service.dart';

import 'package:inventory_management_app/services/notification_service.dart';

/// Provider class for managing product state and operations
class ProductProvider extends ChangeNotifier {
  List<Product> _products = <Product>[];
  final ProductService _productService;
  // Business logic integrated directly
  bool _isLoading = false;
  String? _error;

  /// منشئ افتراضي (للتوافق مع الكود الحالي)
  ProductProvider() : _productService = ProductService();

  /// منشئ مع حقن الاعتماديات
  ProductProvider.withService(this._productService);

  /// Get the list of products
  List<Product> get products => _products;

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Initialize provider and load products from database
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة ProductProvider...');
      await fetchProducts();
      debugPrint('✅ تم تهيئة ProductProvider بنجاح');
    } catch (e, s) {
      debugPrint('❌ خطأ في تهيئة ProductProvider: $e');
      debugPrint('Stack trace: $s');
      _error = 'فشل في تحميل المنتجات: $e';
      notifyListeners();
    }
  }

  /// Load all products from the database (alias for fetchProducts)
  Future<void> loadProducts() async {
    await fetchProducts();
  }

  /// Fetch all products from the database
  Future<void> fetchProducts() async {
    if (_isLoading) return; // تجنب التحميل المتعدد

    _setLoading(true);
    _clearError();

    try {
      final List<Product> products = await _productService.getAllProducts();
      _products = products;
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل المنتجات: $e');
      _setError('Failed to fetch products: $e');
      // Keep existing products if fetch fails
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new product
  Future<void> addProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      // Add to database
      await _productService.insertProduct(product);

      // Add to local list for immediate UI update
      _products.add(product);
      notifyListeners();

      // Send notification for new product
      await NotificationService.instance
          .showNewProductNotification(product.name);

      // Check for low stock and notify
      if ((product.quantity ?? 0) <= 10) {
        await NotificationService.instance.showLowStockNotification(
            product.name, product.quantity.toDouble());
      }

      // Refresh from database to get the ID
      await fetchProducts();
    } catch (e) {
      _setError('Failed to add product: $e');
      // Remove from local list if database operation failed
      _products.removeWhere((Product p) => p.name == product.name);
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing product
  Future<void> updateProduct(Product product) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.updateProduct(product);
      await fetchProducts();
    } catch (e) {
      _setError('Failed to update product: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a product
  Future<void> deleteProduct(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.deleteProduct(id);
      await fetchProducts();
    } catch (e) {
      _setError('Failed to delete product: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Search products by name
  Future<void> searchProducts(String name) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.searchProductsByName(name);
      notifyListeners();
    } catch (e) {
      _setError('Failed to search products: $e');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Validate product name uniqueness within category
  Future<bool> validateProductName(String name, int? categoryId,
      {int? excludeId}) async {
    try {
      // Simple validation - check if name exists in same category
      final List<Product> existingProducts =
          await _productService.getAllProducts();
      return !existingProducts.any((Product p) =>
          p.name.toLowerCase() == name.toLowerCase() &&
          p.categoryId == categoryId &&
          p.id != excludeId);
    } catch (e) {
      _setError('Failed to validate product name: $e');
      return false;
    }
  }

  /// Get low stock products
  Future<List<Map<String, dynamic>>> getLowStockReport(double threshold) async {
    try {
      // Simple low stock report
      final List<Product> allProducts = await _productService.getAllProducts();
      return allProducts
          .where((Product p) => (p.storeQuantity ?? 0) < threshold)
          .map((Product p) => <String, Object?>{
                'id': p.id,
                'name': p.name,
                'currentStock': p.storeQuantity ?? 0,
                'threshold': threshold,
              })
          .toList();
    } catch (e) {
      _setError('Failed to get low stock report: $e');
      return <Map<String, dynamic>>[];
    }
  }

  /// Get products by category with loading state
  Future<void> getProductsByCategory(int categoryId) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getProductsByCategory(categoryId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get products by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get products by supplier with loading state
  Future<void> getProductsBySupplier(int supplierId) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getProductsBySupplier(supplierId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get products by supplier: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get low stock products with loading state
  Future<void> getLowStockProducts(double threshold) async {
    _setLoading(true);
    _clearError();

    try {
      _products = await _productService.getLowStockProducts(threshold);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get low stock products: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update product quantity (warehouse and store)
  Future<void> updateProductQuantity(
      int productId, int warehouseQuantity, int storeQuantity) async {
    _setLoading(true);
    _clearError();

    try {
      await _productService.updateProductQuantity(
          productId, warehouseQuantity, storeQuantity);
      await fetchProducts(); // Refresh the list
    } catch (e) {
      _setError('Failed to update product quantity: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get product count
  Future<int> getProductCount() async {
    try {
      return await _productService.getProductCount();
    } catch (e) {
      _setError('Failed to get product count: $e');
      return 0;
    }
  }

  /// Clear all products (useful for filtering)
  void clearProducts() {
    _products.clear();
    notifyListeners();
  }

  /// Reset to show all products
  Future<void> resetProducts() async {
    await fetchProducts();
  }

  /// Decrease warehouse quantity for a product (for wholesale sales)
  Future<void> decreaseWarehouseQuantity(int productId, double quantity) async {
    try {
      // Find the product in the local list
      final int productIndex =
          _products.indexWhere((Product p) => p.id == productId);
      if (productIndex == -1) {
        throw Exception('المنتج غير موجود');
      }

      final Product product = _products[productIndex];
      final int currentWarehouseQuantity = product.warehouseQuantity ?? 0;

      // Check if there's enough quantity in warehouse
      if (currentWarehouseQuantity < quantity) {
        throw Exception('الكمية المطلوبة أكبر من المتوفر في المخزن');
      }

      // Update warehouse quantity
      final Product updatedProduct = Product(
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        quantity: product.quantity,
        categoryId: product.categoryId,
        unitId: product.unitId,
        supplierId: product.supplierId,
        category: product.category,
        unit: product.unit,
        purchasePrice: product.purchasePrice,
        salePrice: product.salePrice,
        minLevel: product.minLevel,
        barcode: product.barcode,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        date: product.date,
        retailPrice: product.retailPrice,
        warehouseQuantity: currentWarehouseQuantity - quantity.toInt(),
        storeQuantity: product.storeQuantity,
      );

      // Update in database
      await _productService.updateProduct(updatedProduct);

      // Update local list
      _products[productIndex] = updatedProduct;
      notifyListeners();

      // Check for low stock notification
      if ((updatedProduct.warehouseQuantity ?? 0) <=
          (updatedProduct.minLevel ?? 10)) {
        await NotificationService.instance.showLowStockNotification(
          updatedProduct.name,
          (updatedProduct.warehouseQuantity ?? 0).toDouble(),
        );
      }
    } catch (e) {
      _setError('فشل في تقليل كمية المخزن: $e');
      rethrow;
    }
  }

  /// Decrease store quantity for a product (for retail sales)
  Future<void> decreaseStoreQuantity(int productId, double quantity) async {
    try {
      // Find the product in the local list
      final int productIndex =
          _products.indexWhere((Product p) => p.id == productId);
      if (productIndex == -1) {
        throw Exception('المنتج غير موجود');
      }

      final Product product = _products[productIndex];
      final int currentStoreQuantity = product.storeQuantity ?? 0;

      // Check if there's enough quantity in store
      if (currentStoreQuantity < quantity) {
        throw Exception('الكمية المطلوبة أكبر من المتوفر في البقالة');
      }

      // Update store quantity
      final Product updatedProduct = Product(
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        quantity: product.quantity,
        categoryId: product.categoryId,
        unitId: product.unitId,
        supplierId: product.supplierId,
        category: product.category,
        unit: product.unit,
        purchasePrice: product.purchasePrice,
        salePrice: product.salePrice,
        minLevel: product.minLevel,
        barcode: product.barcode,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        date: product.date,
        retailPrice: product.retailPrice,
        warehouseQuantity: product.warehouseQuantity,
        storeQuantity: currentStoreQuantity - quantity.toInt(),
      );

      // Update in database
      await _productService.updateProduct(updatedProduct);

      // Update local list
      _products[productIndex] = updatedProduct;
      notifyListeners();

      // Check for low stock notification
      if ((updatedProduct.storeQuantity ?? 0) <=
          (updatedProduct.minLevel ?? 10)) {
        await NotificationService.instance.showLowStockNotification(
          '${updatedProduct.name} (البقالة)',
          (updatedProduct.storeQuantity ?? 0).toDouble(),
        );
      }
    } catch (e) {
      _setError('فشل في تقليل كمية البقالة: $e');
      rethrow;
    }
  }

  /// Transfer quantity from warehouse to store (for internal transfers)
  Future<bool> transferWarehouseToStore(int productId, int quantity) async {
    try {
      // Find the product in the local list
      final int productIndex =
          _products.indexWhere((Product p) => p.id == productId);
      if (productIndex == -1) {
        throw Exception('المنتج غير موجود');
      }

      final Product product = _products[productIndex];
      final int currentWarehouseQuantity = product.warehouseQuantity ?? 0;
      final int currentStoreQuantity = product.storeQuantity ?? 0;

      // Check if there's enough quantity in warehouse
      if (currentWarehouseQuantity < quantity) {
        throw Exception(
            'الكمية المطلوبة ($quantity) أكبر من المتوفر في المخزن ($currentWarehouseQuantity)');
      }

      // Update both quantities
      final Product updatedProduct = Product(
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        quantity: product.quantity,
        categoryId: product.categoryId,
        unitId: product.unitId,
        supplierId: product.supplierId,
        category: product.category,
        unit: product.unit,
        purchasePrice: product.purchasePrice,
        salePrice: product.salePrice,
        minLevel: product.minLevel,
        barcode: product.barcode,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        date: product.date,
        retailPrice: product.retailPrice,
        warehouseQuantity: currentWarehouseQuantity - quantity,
        storeQuantity: currentStoreQuantity + quantity,
      );

      // Update in database
      await _productService.updateProduct(updatedProduct);

      // Update local list
      _products[productIndex] = updatedProduct;
      notifyListeners();

      debugPrint(
          '✅ تم تحويل $quantity من المنتج ${product.name} من المخزن إلى البقالة');
      return true;
    } catch (e) {
      _setError('فشل في نقل الكمية من المخزن إلى البقالة: $e');
      debugPrint('❌ خطأ في التحويل: $e');
      rethrow;
    }
  }

  /// Get product by ID for transfer operations
  Product? getProductById(int productId) {
    try {
      return _products.firstWhere((Product product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Check if product has sufficient warehouse quantity for transfer
  bool canTransferFromWarehouse(int productId, int quantity) {
    final Product? product = getProductById(productId);
    if (product == null) return false;

    final int warehouseQuantity = product.warehouseQuantity ?? 0;
    return warehouseQuantity >= quantity;
  }

  /// Get total warehouse quantity across all products
  int getTotalWarehouseQuantity() {
    return _products.fold<int>(0,
        (int sum, Product product) => sum + (product.warehouseQuantity ?? 0));
  }

  /// Get total store quantity across all products
  int getTotalStoreQuantity() {
    return _products.fold<int>(
        0, (int sum, Product product) => sum + (product.storeQuantity ?? 0));
  }

  /// Get total inventory value at warehouse prices
  double getTotalWarehouseValue() {
    return _products.fold<double>(0.0, (double sum, Product product) {
      final int quantity = product.warehouseQuantity ?? 0;
      final double price = product.price ?? 0;
      return sum + (quantity * price);
    });
  }

  /// Get total inventory value at retail prices
  double getTotalStoreValue() {
    return _products.fold<double>(0.0, (double sum, Product product) {
      final int quantity = product.storeQuantity ?? 0;
      final double price = product.retailPrice ?? 0;
      return sum + (quantity * price);
    });
  }

  /// Get products with low warehouse stock
  List<Product> getLowWarehouseStockProducts() {
    return _products.where((Product product) {
      final int warehouseQuantity = product.warehouseQuantity ?? 0;
      final int minLevel = product.minLevel ?? 10;
      return warehouseQuantity <= minLevel;
    }).toList();
  }

  /// Get products with low store stock
  List<Product> getLowStoreStockProducts() {
    return _products.where((Product product) {
      final int storeQuantity = product.storeQuantity ?? 0;
      final int minLevel = product.minLevel ?? 10;
      return storeQuantity <= minLevel;
    }).toList();
  }

  /// Get inventory distribution data for analytics
  Map<String, dynamic> getInventoryDistribution() {
    final int totalWarehouse = getTotalWarehouseQuantity();
    final int totalStore = getTotalStoreQuantity();
    final int totalProducts = _products.length;

    return <String, dynamic>{
      'totalWarehouseQuantity': totalWarehouse,
      'totalStoreQuantity': totalStore,
      'totalProducts': totalProducts,
      'warehouseValue': getTotalWarehouseValue(),
      'storeValue': getTotalStoreValue(),
      'lowWarehouseStock': getLowWarehouseStockProducts().length,
      'lowStoreStock': getLowStoreStockProducts().length,
    };
  }

  /// Update store quantity for a product (for inventory adjustments)
  Future<bool> updateStoreQuantity(int productId, int newQuantity) async {
    try {
      // Find the product in the local list
      final int productIndex =
          _products.indexWhere((Product p) => p.id == productId);
      if (productIndex == -1) {
        throw Exception('المنتج غير موجود');
      }

      final Product product = _products[productIndex];

      // Update store quantity
      final Product updatedProduct = Product(
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        quantity: product.quantity,
        categoryId: product.categoryId,
        unitId: product.unitId,
        supplierId: product.supplierId,
        category: product.category,
        unit: product.unit,
        purchasePrice: product.purchasePrice,
        salePrice: product.salePrice,
        minLevel: product.minLevel,
        barcode: product.barcode,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        date: product.date,
        retailPrice: product.retailPrice,
        warehouseQuantity: product.warehouseQuantity,
        storeQuantity: newQuantity,
      );

      // Update in database
      await _productService.updateProduct(updatedProduct);

      // Update local list
      _products[productIndex] = updatedProduct;
      notifyListeners();

      debugPrint(
          '✅ تم تحديث كمية البقالة للمنتج ${product.name} إلى $newQuantity');
      return true;
    } catch (e) {
      _setError('فشل في تحديث كمية البقالة: $e');
      debugPrint('❌ خطأ في تحديث كمية البقالة: $e');
      rethrow;
    }
  }

  /// استيراد المنتجات من ملف إكسل
  Future<Map<String, dynamic>> importProductsFromExcel(
      List<Map<String, dynamic>> productsData) async {
    _setLoading(true);
    _clearError();

    int importedCount = 0;
    int updatedCount = 0;
    int skippedCount = 0;
    List<String> errors = <String>[];

    try {
      debugPrint('🔄 بدء استيراد ${productsData.length} منتج من إكسل...');

      for (final Map<String, dynamic> productData in productsData) {
        try {
          // استخراج البيانات الأساسية
          final String? name = _extractStringValue(
              productData, <String>['name', 'الاسم', 'اسم المنتج']);

          if (name == null || name.trim().isEmpty) {
            skippedCount++;
            errors.add('تم تخطي منتج بدون اسم');
            continue;
          }

          // البحث عن منتج موجود بنفس الاسم
          final Product? existingProduct =
              await _findExistingProduct(name.trim());

          if (existingProduct != null) {
            // تحديث المنتج الموجود
            final Product updatedProduct =
                _createUpdatedProduct(existingProduct, productData);
            await _productService.updateProduct(updatedProduct);
            updatedCount++;
            debugPrint('✅ تم تحديث المنتج: $name');
          } else {
            // إنشاء منتج جديد
            final Product newProduct = _createNewProduct(productData);
            await _productService.insertProduct(newProduct);
            importedCount++;
            debugPrint('✅ تم إضافة منتج جديد: $name');
          }
        } catch (e) {
          final String productName = _extractStringValue(
                  productData, <String>['name', 'الاسم', 'اسم المنتج']) ??
              'منتج غير معروف';
          errors.add('خطأ في معالجة $productName: $e');
          skippedCount++;
          debugPrint('❌ خطأ في معالجة المنتج $productName: $e');
        }
      }

      // إعادة تحميل قائمة المنتجات
      await fetchProducts();

      debugPrint(
          '🎉 انتهى الاستيراد - مستورد: $importedCount، محدث: $updatedCount، متخطى: $skippedCount');

      return <String, dynamic>{
        'success': true,
        'imported': importedCount,
        'updated': updatedCount,
        'skipped': skippedCount,
        'errors': errors,
      };
    } catch (e) {
      _setError('فشل في استيراد المنتجات: $e');
      debugPrint('❌ خطأ عام في الاستيراد: $e');
      return <String, dynamic>{
        'success': false,
        'imported': importedCount,
        'updated': updatedCount,
        'skipped': skippedCount,
        'errors': <String>[...errors, e.toString()],
      };
    } finally {
      _setLoading(false);
    }
  }

  /// البحث عن منتج موجود بالاسم
  Future<Product?> _findExistingProduct(String name) async {
    try {
      final List<Product> products =
          await _productService.searchProductsByName(name);
      return products.firstWhere(
        (Product product) =>
            product.name.toLowerCase().trim() == name.toLowerCase().trim(),
        orElse: () => throw Exception('لا يوجد'),
      );
    } catch (e) {
      return null;
    }
  }

  /// إنشاء منتج جديد من بيانات إكسل
  Product _createNewProduct(Map<String, dynamic> data) {
    return Product(
      name:
          _extractStringValue(data, <String>['name', 'الاسم', 'اسم المنتج']) ??
              '',
      description:
          _extractStringValue(data, <String>['description', 'الوصف', 'وصف']) ??
              '',
      unit:
          _extractStringValue(data, <String>['unit', 'الوحدة', 'وحدة القياس']),
      category:
          _extractStringValue(data, <String>['category', 'الفئة', 'التصنيف']),
      barcode: _extractStringValue(
          data, <String>['barcode', 'الباركود', 'رمز المنتج']),

      // الأسعار
      price: _extractDoubleValue(
              data, <String>['wholesalePrice', 'سعر الجملة', 'جملة']) ??
          0.0,
      retailPrice: _extractDoubleValue(
          data, <String>['retailPrice', 'سعر التجزئة', 'تجزئة']),
      purchasePrice: _extractDoubleValue(
          data, <String>['purchasePrice', 'سعر الشراء', 'شراء']),
      salePrice: _extractDoubleValue(
          data, <String>['retailPrice', 'سعر التجزئة', 'تجزئة']),

      // الكميات
      warehouseQuantity: _extractIntValue(
          data, <String>['warehouseQuantity', 'كمية المخزن', 'مخزن']),
      storeQuantity: _extractIntValue(
          data, <String>['storeQuantity', 'كمية البقالة', 'بقالة']),
      quantity:
          _extractDoubleValue(data, <String>['quantity', 'الكمية', 'كمية']) ??
              0,

      // معلومات إضافية
      minLevel: (_extractDoubleValue(
                  data, <String>['minLevel', 'الحد الأدنى', 'حد أدنى']) ??
              10)
          .toInt(),

      // التواريخ
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      date: DateTime.now().toIso8601String(),
    );
  }

  /// تحديث منتج موجود ببيانات من إكسل
  Product _createUpdatedProduct(
      Product existingProduct, Map<String, dynamic> data) {
    return Product(
      id: existingProduct.id,
      name: existingProduct.name, // لا نغير الاسم
      description:
          _extractStringValue(data, <String>['description', 'الوصف', 'وصف']) ??
              existingProduct.description,
      unit: _extractStringValue(
              data, <String>['unit', 'الوحدة', 'وحدة القياس']) ??
          existingProduct.unit,
      category:
          _extractStringValue(data, <String>['category', 'الفئة', 'التصنيف']) ??
              existingProduct.category,
      barcode: _extractStringValue(
              data, <String>['barcode', 'الباركود', 'رمز المنتج']) ??
          existingProduct.barcode,

      // تحديث الأسعار إذا كانت موجودة في الإكسل
      price: _extractDoubleValue(
              data, <String>['wholesalePrice', 'سعر الجملة', 'جملة']) ??
          existingProduct.price,
      retailPrice: _extractDoubleValue(
              data, <String>['retailPrice', 'سعر التجزئة', 'تجزئة']) ??
          existingProduct.retailPrice,
      purchasePrice: _extractDoubleValue(
              data, <String>['purchasePrice', 'سعر الشراء', 'شراء']) ??
          existingProduct.purchasePrice,
      salePrice: _extractDoubleValue(
              data, <String>['retailPrice', 'سعر التجزئة', 'تجزئة']) ??
          existingProduct.salePrice,

      // تحديث الكميات (إضافة للكمية الموجودة)
      warehouseQuantity: _updateQuantity(
          existingProduct.warehouseQuantity,
          _extractIntValue(
              data, <String>['warehouseQuantity', 'كمية المخزن', 'مخزن'])),
      storeQuantity: _updateQuantity(
          existingProduct.storeQuantity,
          _extractIntValue(
              data, <String>['storeQuantity', 'كمية البقالة', 'بقالة'])),
      quantity:
          _extractDoubleValue(data, <String>['quantity', 'الكمية', 'كمية']) ??
              existingProduct.quantity,

      // الحفاظ على البيانات الأخرى
      categoryId: existingProduct.categoryId,
      unitId: existingProduct.unitId,
      supplierId: existingProduct.supplierId,
      minLevel: (_extractDoubleValue(
                  data, <String>['minLevel', 'الحد الأدنى', 'حد أدنى']) ??
              (existingProduct.minLevel ?? 10))
          .toInt(),

      // تحديث التاريخ
      createdAt: existingProduct.createdAt,
      updatedAt: DateTime.now(),
      date: existingProduct.date,
    );
  }

  /// استخراج قيمة نصية من البيانات
  String? _extractStringValue(
      Map<String, dynamic> data, List<String> possibleKeys) {
    for (final String key in possibleKeys) {
      if (data.containsKey(key) && data[key] != null) {
        final String value = data[key].toString().trim();
        return value.isNotEmpty ? value : null;
      }
    }
    return null;
  }

  /// استخراج قيمة رقمية عشرية من البيانات
  double? _extractDoubleValue(
      Map<String, dynamic> data, List<String> possibleKeys) {
    for (final String key in possibleKeys) {
      if (data.containsKey(key) && data[key] != null) {
        try {
          final value = data[key];
          if (value is num) {
            return value.toDouble();
          } else if (value is String) {
            final String cleanValue = value.replaceAll(',', '').trim();
            if (cleanValue.isNotEmpty) {
              return double.parse(cleanValue);
            }
          }
        } catch (e) {
          // تجاهل الأخطاء والمحاولة مع المفتاح التالي
        }
      }
    }
    return null;
  }

  /// استخراج قيمة رقمية صحيحة من البيانات
  int? _extractIntValue(Map<String, dynamic> data, List<String> possibleKeys) {
    final double? doubleValue = _extractDoubleValue(data, possibleKeys);
    return doubleValue?.round();
  }

  /// تحديث الكمية (إضافة أو استبدال)
  int? _updateQuantity(int? existingQuantity, int? newQuantity) {
    if (newQuantity == null) {
      return existingQuantity;
    }

    // إذا كانت الكمية الجديدة موجبة، نضيفها للكمية الموجودة
    // إذا كانت سالبة أو صفر، نستبدل الكمية الموجودة
    if (newQuantity > 0) {
      return (existingQuantity ?? 0) + newQuantity;
    } else {
      return newQuantity;
    }
  }
}
