import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fast_contacts/fast_contacts.dart';
import '../../providers/supplier_provider.dart';
import '../../models/supplier.dart';
import '../../widgets/modern_drawer.dart';
import '../../utils/navigation_manager.dart';
import '../dialogs/import_contacts_dialog.dart';
import 'supplier_form_screen.dart';
import 'supplier_details_screen.dart';

class SupplierListScreen extends StatefulWidget {
  const SupplierListScreen({super.key});

  @override
  State<SupplierListScreen> createState() => _SupplierListScreenState();
}

class _SupplierListScreenState extends State<SupplierListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedBalanceFilter = 'الكل';
  final String _selectedCategoryFilter = 'الكل';

  final List<String> _balanceFilters = <String>[
    'الكل',
    'عليهم مبالغ (مدينون لنا)',
    'لهم مبالغ (دائنون لنا)',
    'رصيد صفر',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SupplierProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: Scaffold(
          backgroundColor: Colors.white,
          drawer: const ModernDrawer(),
          appBar: AppBar(
            title: const Text('الموردون'),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            leading: Builder(
              builder: (BuildContext context) => IconButton(
                icon: const Icon(Icons.menu, color: Colors.white),
                onPressed: () => Scaffold.of(context).openDrawer(),
              ),
            ),
            actions: <Widget>[
              IconButton(
                icon: const Icon(Icons.contact_phone),
                onPressed: _importFromContacts,
                tooltip: 'استيراد من جهات الاتصال',
              ),
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: _showSearchDialog,
              ),
              IconButton(
                icon: const Icon(Icons.add_circle),
                onPressed: _addNewSupplier,
              ),
            ],
          ),
          body: Consumer<SupplierProvider>(
            builder: (BuildContext context, SupplierProvider supplierProvider,
                Widget? child) {
              if (supplierProvider.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.green),
                );
              }

              if (supplierProvider.error != null) {
                return _buildErrorWidget(supplierProvider.error!);
              }

              final List<Supplier> suppliers = supplierProvider.suppliers;

              return Column(
                children: <Widget>[
                  // شريط البحث والتصفية
                  _buildSearchAndFilterBar(supplierProvider),

                  // قائمة الموردين
                  Expanded(
                    child: suppliers.isEmpty
                        ? _buildEmptyState()
                        : _buildSuppliersList(suppliers),
                  ),
                ],
              );
            },
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: _addNewSupplier,
            backgroundColor: Colors.green,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchAndFilterBar(SupplierProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Column(
        children: <Widget>[
          // شريط البحث
          TextFormField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالاسم أو رقم الهاتف...',
              prefixIcon: const Icon(Icons.search, color: Colors.green),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        provider.searchSuppliers('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.green),
              ),
            ),
            onChanged: (String value) {
              provider.searchSuppliers(value);
            },
          ),

          const SizedBox(height: 12),

          // عوامل التصفية
          Row(
            children: <Widget>[
              // تصفية الرصيد
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedBalanceFilter,
                  decoration: InputDecoration(
                    labelText: 'تصفية الرصيد',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items: _balanceFilters.map((String filter) {
                    return DropdownMenuItem(
                      value: filter,
                      child: Text(filter, style: const TextStyle(fontSize: 14)),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      setState(() {
                        _selectedBalanceFilter = value;
                      });
                      provider.filterByBalance(value);
                    }
                  },
                ),
              ),

              const SizedBox(width: 12),

              // زر تطبيق التصفية
              ElevatedButton(
                onPressed: () {
                  provider.filterByBalance(_selectedBalanceFilter);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('تطبيق'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuppliersList(List<Supplier> suppliers) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: suppliers.length,
      itemBuilder: (BuildContext context, int index) {
        final Supplier supplier = suppliers[index];
        return _buildSupplierCard(supplier);
      },
    );
  }

  Widget _buildSupplierCard(Supplier supplier) {
    Color balanceColor = Colors.grey;
    if (supplier.balanceColor == 'green') balanceColor = Colors.green;
    if (supplier.balanceColor == 'red') balanceColor = Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        supplier.name ?? 'مورد غير محدد',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (supplier.phone != null)
                        Text(
                          supplier.phone!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      supplier.formattedBalance,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: balanceColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      supplier.balanceStatus,
                      style: TextStyle(
                        fontSize: 12,
                        color: balanceColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                _buildActionButton(
                  icon: Icons.visibility,
                  label: 'عرض',
                  color: Colors.blue,
                  onPressed: () => _viewSupplierDetails(supplier),
                ),
                _buildActionButton(
                  icon: Icons.edit,
                  label: 'تعديل',
                  color: Colors.orange,
                  onPressed: () => _editSupplier(supplier),
                ),
                _buildActionButton(
                  icon: Icons.delete,
                  label: 'حذف',
                  color: Colors.red,
                  onPressed: () => _deleteSupplier(supplier),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 16),
          label: Text(label, style: const TextStyle(fontSize: 12)),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.local_shipping_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد موردون',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإضافة مورد جديد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _addNewSupplier,
              icon: const Icon(Icons.add),
              label: const Text('إضافة مورد جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'حدث خطأ في تحميل الموردين',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.read<SupplierProvider>().loadSuppliers();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('البحث عن مورد'),
        content: TextFormField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'اكتب اسم المورد أو رقم الهاتف',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context
                  .read<SupplierProvider>()
                  .searchSuppliers(_searchController.text);
              Navigator.pop(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _addNewSupplier() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => const CreateEditSupplierScreen(),
      ),
    ).then((_) {
      // إعادة تحميل القائمة بعد الإضافة
      context.read<SupplierProvider>().loadSuppliers();
    });
  }

  void _viewSupplierDetails(Supplier supplier) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) =>
            SupplierDetailsScreen(supplier: supplier),
      ),
    );
  }

  void _editSupplier(Supplier supplier) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تأكيد التعديل'),
        content:
            const Text('هل أنت متأكد من رغبتك في تعديل بيانات هذا المورد؟'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (BuildContext context) =>
              CreateEditSupplierScreen(supplier: supplier),
        ),
      ).then((_) {
        // إعادة تحميل القائمة بعد التعديل
        context.read<SupplierProvider>().loadSuppliers();
      });
    }
  }

  void _deleteSupplier(Supplier supplier) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذا المورد؟ سيتم حذف جميع الفواتير والمبالغ المرتبطة به. لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final bool success =
          await context.read<SupplierProvider>().deleteSupplier(supplier.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(success ? 'تم حذف المورد بنجاح' : 'فشل في حذف المورد'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  /// استيراد الموردين من جهات الاتصال
  Future<void> _importFromContacts() async {
    try {
      await showDialog(
        context: context,
        builder: (BuildContext context) => ImportContactsDialog(
          type: 'supplier',
          onContactsSelected: _handleContactsImport,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ في فتح جهات الاتصال: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// معالجة استيراد جهات الاتصال المحددة
  Future<void> _handleContactsImport(List<Contact> selectedContacts) async {
    if (selectedContacts.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لم يتم اختيار أي جهة اتصال'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    try {
      // عرض مؤشر التحميل
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }

      // استيراد جهات الاتصال
      final SupplierProvider supplierProvider =
          context.read<SupplierProvider>();
      final Map<String, dynamic> result =
          await supplierProvider.importSuppliersFromContacts(selectedContacts);

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.pop(context);
      }

      // عرض النتائج
      if (result['success'] == true) {
        final int imported = result['imported'] as int;
        final int skipped = result['skipped'] as int;

        String message = 'تم استيراد $imported مورد بنجاح';
        if (skipped > 0) {
          message +=
              '\nتم تخطي $skipped جهة اتصال (موجودة مسبقاً أو غير صالحة)';
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else {
        final List<String> errors = result['errors'] as List<String>;
        String errorMessage = 'فشل في استيراد جهات الاتصال';
        if (errors.isNotEmpty) {
          errorMessage += ':\n${errors.first}';
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء الاستيراد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
