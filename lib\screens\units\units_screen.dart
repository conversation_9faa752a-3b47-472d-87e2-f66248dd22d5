import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:inventory_management_app/providers/unit_provider.dart';
import 'package:inventory_management_app/models/unit.dart';

class UnitsScreen extends StatelessWidget {
  const UnitsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final UnitProvider unitProvider = Provider.of<UnitProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Units'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // TODO: إضافة مسار للوحدات في app_router.dart
              context.push('/units/add');
            },
          ),
        ],
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: unitProvider.units.length,
        itemBuilder: (BuildContext context, int index) {
          final Unit unit = unitProvider.units[index];
          return Card(
            child: ListTile(
              title: Text(unit.name),
              subtitle: Text(unit.symbol ?? ''),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      // TODO: إضافة مسار للوحدات في app_router.dart
                      context.push('/units/edit/${unit.id}');
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      unitProvider.deleteUnit(unit.id!);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
