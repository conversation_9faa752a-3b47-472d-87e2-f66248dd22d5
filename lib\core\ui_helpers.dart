/// مساعدات واجهة المستخدم - فصل منطق الأعمال عن BuildContext
///
/// يحتوي على دوال مساعدة لعرض الرسائل والحوارات
/// بدون الحاجة لتمرير BuildContext عبر طبقات المنطق
///
/// المطور: فريق أسامة ماركت
/// التاريخ: 2024
library ui_helpers;

import 'package:flutter/material.dart';

/// نوع دالة لعرض الرسائل
typedef MessageCallback = void Function(String message);

/// نوع دالة لعرض الحوارات
typedef DialogCallback = Future<bool?> Function(String title, String content);

/// نوع دالة لعرض مؤشر التحميل
typedef LoadingCallback = void Function(bool isLoading);

/// فئة مساعدة لإدارة callbacks واجهة المستخدم
class UICallbacks {
  static MessageCallback? _showSuccess;
  static MessageCallback? _showError;
  static MessageCallback? _showWarning;
  static MessageCallback? _showInfo;
  static DialogCallback? _showConfirmDialog;
  static LoadingCallback? _showLoading;

  /// تسجيل callbacks واجهة المستخدم
  static void register({
    MessageCallback? showSuccess,
    MessageCallback? showError,
    MessageCallback? showWarning,
    MessageCallback? showInfo,
    DialogCallback? showConfirmDialog,
    LoadingCallback? showLoading,
  }) {
    _showSuccess = showSuccess;
    _showError = showError;
    _showWarning = showWarning;
    _showInfo = showInfo;
    _showConfirmDialog = showConfirmDialog;
    _showLoading = showLoading;
  }

  /// عرض رسالة نجاح
  static void showSuccess(String message) {
    _showSuccess?.call(message);
  }

  /// عرض رسالة خطأ
  static void showError(String message) {
    _showError?.call(message);
  }

  /// عرض رسالة تحذير
  static void showWarning(String message) {
    _showWarning?.call(message);
  }

  /// عرض رسالة معلومات
  static void showInfo(String message) {
    _showInfo?.call(message);
  }

  /// عرض حوار تأكيد
  static Future<bool?> showConfirmDialog(String title, String content) {
    return _showConfirmDialog?.call(title, content) ?? Future.value(false);
  }

  /// عرض/إخفاء مؤشر التحميل
  static void showLoading(bool isLoading) {
    _showLoading?.call(isLoading);
  }

  /// إلغاء تسجيل جميع callbacks
  static void unregister() {
    _showSuccess = null;
    _showError = null;
    _showWarning = null;
    _showInfo = null;
    _showConfirmDialog = null;
    _showLoading = null;
  }
}

/// مساعد لتسجيل callbacks من BuildContext
class UIHelper {
  /// تسجيل callbacks من الشاشة الحالية
  static void registerCallbacks(BuildContext context) {
    UICallbacks.register(
      showSuccess: (String message) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      showError: (String message) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      showWarning: (String message) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      showInfo: (String message) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.blue,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      showConfirmDialog: (String title, String content) async {
        if (!context.mounted) return false;
        
        return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('تأكيد'),
              ),
            ],
          ),
        );
      },
      showLoading: (bool isLoading) {
        if (!context.mounted) return;
        
        if (isLoading) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) => const Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else {
          Navigator.of(context).pop();
        }
      },
    );
  }
}

/// نتيجة عملية مع رسائل واجهة المستخدم
class OperationResult<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? errorMessage;

  const OperationResult({
    required this.success,
    this.data,
    this.message,
    this.errorMessage,
  });

  /// إنشاء نتيجة ناجحة
  factory OperationResult.success({T? data, String? message}) {
    return OperationResult<T>(
      success: true,
      data: data,
      message: message,
    );
  }

  /// إنشاء نتيجة فاشلة
  factory OperationResult.failure({String? errorMessage}) {
    return OperationResult<T>(
      success: false,
      errorMessage: errorMessage,
    );
  }

  /// عرض النتيجة للمستخدم
  void showToUser() {
    if (success) {
      if (message != null) {
        UICallbacks.showSuccess(message!);
      }
    } else {
      if (errorMessage != null) {
        UICallbacks.showError(errorMessage!);
      }
    }
  }
}

/// مساعد لتنفيذ العمليات مع معالجة الأخطاء
class OperationHelper {
  /// تنفيذ عملية مع معالجة الأخطاء وعرض النتائج
  static Future<OperationResult<T>> execute<T>(
    Future<T> Function() operation, {
    String? successMessage,
    String? errorPrefix,
  }) async {
    try {
      final T result = await operation();
      final OperationResult<T> operationResult = OperationResult.success(
        data: result,
        message: successMessage,
      );
      operationResult.showToUser();
      return operationResult;
    } catch (e) {
      final String errorMessage = errorPrefix != null 
          ? '$errorPrefix: $e' 
          : e.toString();
      final OperationResult<T> operationResult = OperationResult.failure(
        errorMessage: errorMessage,
      );
      operationResult.showToUser();
      return operationResult;
    }
  }
}
