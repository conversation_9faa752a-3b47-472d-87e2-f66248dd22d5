import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../utils/screen_utils.dart';

/// شريط التنقل السفلي الشرطي - يظهر فقط في الشاشات الرئيسية
class ConditionalBottomNav extends StatelessWidget {
  /// المحتوى الرئيسي للشاشة
  final Widget child;

  /// إجبار إظهار شريط التنقل (اختياري)
  final bool? forceShow;

  /// إجبار إخفاء شريط التنقل (اختياري)
  final bool? forceHide;

  const ConditionalBottomNav({
    super.key,
    required this.child,
    this.forceShow,
    this.forceHide,
  });

  @override
  Widget build(BuildContext context) {
    final String currentRoute = GoRouterState.of(context).uri.path;

    // تحديد ما إذا كان يجب إظهار شريط التنقل
    bool shouldShowBottomNav;

    if (forceHide == true) {
      shouldShowBottomNav = false;
    } else if (forceShow == true) {
      shouldShowBottomNav = true;
    } else {
      shouldShowBottomNav = ScreenUtils.isMainScreen(currentRoute);
    }

    return Scaffold(
      body: child,
      bottomNavigationBar: shouldShowBottomNav
          ? BottomNavigationBar(
              type: BottomNavigationBarType.fixed,
              currentIndex: _getCurrentIndex(currentRoute),
              onTap: (int index) => _onTabTapped(context, index),
              items: const <BottomNavigationBarItem>[
                BottomNavigationBarItem(
                  icon: Icon(Icons.dashboard),
                  label: 'الرئيسية',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.inventory_2),
                  label: 'المنتجات',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.people),
                  label: 'العملاء',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.point_of_sale),
                  label: 'المبيعات',
                ),
                BottomNavigationBarItem(
                  icon: Icon(Icons.assessment),
                  label: 'التقارير',
                ),
              ],
            )
          : null,
    );
  }

  int _getCurrentIndex(String currentRoute) {
    switch (currentRoute) {
      case '/':
      case '/dashboard':
        return 0;
      case '/products':
        return 1;
      case '/customers':
        return 2;
      case '/sales':
        return 3;
      case '/reports':
      case '/analytics':
        return 4;
      default:
        return 0;
    }
  }

  void _onTabTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/products');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/sales');
        break;
      case 4:
        context.go('/reports');
        break;
    }
  }
}

/// Wrapper للشاشات الرئيسية مع شريط التنقل
class MainScreenWrapper extends StatelessWidget {
  /// المحتوى الرئيسي للشاشة
  final Widget child;

  /// العنوان
  final String title;

  /// الإجراءات في الـ AppBar
  final List<Widget>? actions;

  /// هل تحتاج drawer
  final bool needsDrawer;

  /// هل تحتاج زر رجوع
  final bool needsBackButton;

  const MainScreenWrapper({
    super.key,
    required this.child,
    required this.title,
    this.actions,
    this.needsDrawer = false,
    this.needsBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return ConditionalBottomNav(
      forceShow: true, // الشاشات الرئيسية تحتاج شريط التنقل دائماً
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            title,
            style: const TextStyle(
              fontFamily: 'Tajawal',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Theme.of(context).primaryColor,
          elevation: 0,
          centerTitle: true,
          leading: needsDrawer
              ? Builder(
                  builder: (BuildContext context) => IconButton(
                    icon: const Icon(Icons.menu, color: Colors.white),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                  ),
                )
              : needsBackButton
                  ? IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () {
                        if (Navigator.of(context).canPop()) {
                          Navigator.of(context).pop();
                        } else {
                          context.go('/');
                        }
                      },
                    )
                  : null,
          actions: actions,
        ),
        body: child,
      ),
    );
  }
}

/// Wrapper للشاشات الثانوية بدون شريط التنقل
class SecondaryScreenWrapper extends StatelessWidget {
  /// المحتوى الرئيسي للشاشة
  final Widget child;

  /// العنوان
  final String title;

  /// الإجراءات في الـ AppBar
  final List<Widget>? actions;

  const SecondaryScreenWrapper({
    super.key,
    required this.child,
    required this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return ConditionalBottomNav(
      forceHide: true, // الشاشات الثانوية لا تحتاج شريط التنقل
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            title,
            style: const TextStyle(
              fontFamily: 'Tajawal',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Theme.of(context).primaryColor,
          elevation: 0,
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                context.go('/');
              }
            },
          ),
          actions: actions,
        ),
        body: child,
      ),
    );
  }
}
