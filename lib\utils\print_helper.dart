import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../config/app_constants.dart';
import '../utils/date_helper.dart';
import '../utils/formatters.dart';
import '../models/product.dart';
import '../models/customer.dart';
import '../models/sale.dart';
import '../models/purchase.dart';

/// فئة مساعدة للطباعة
class PrintHelper {
  /// طباعة فاتورة البيع
  static Future<Uint8List?> generateSaleInvoice(
      Sale sale, List<Map<String, dynamic>> items) async {
    try {
      return await _generateSaleInvoicePDF(sale, items);
    } catch (e, stack) {
      debugPrint('خطأ في إنشاء فاتورة البيع: $e\n$stack');
      return null;
    }
  }

  /// طباعة فاتورة الشراء
  static Future<Uint8List?> generatePurchaseInvoice(
      Purchase purchase, List<Map<String, dynamic>> items) async {
    try {
      return await _generatePurchaseInvoicePDF(purchase, items);
    } catch (e, stack) {
      debugPrint('خطأ في إنشاء فاتورة الشراء: $e\n$stack');
      return null;
    }
  }

  /// طباعة قائمة المنتجات
  static Future<Uint8List?> generateProductsList(List<Product> products) async {
    try {
      return await _generateProductsListPDF(products);
    } catch (e, stack) {
      debugPrint('خطأ في إنشاء قائمة المنتجات: $e\n$stack');
      return null;
    }
  }

  /// طباعة قائمة العملاء
  static Future<Uint8List?> generateCustomersList(
      List<Customer> customers) async {
    try {
      return await _generateCustomersListPDF(customers);
    } catch (e, stack) {
      debugPrint('خطأ في إنشاء قائمة العملاء: $e\n$stack');
      return null;
    }
  }

  /// طباعة تقرير المبيعات
  static Future<Uint8List?> generateSalesReport(
    List<Sale> sales,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _generateSalesReportPDF(sales, startDate, endDate);
    } catch (e, stack) {
      debugPrint('خطأ في إنشاء تقرير المبيعات: $e\n$stack');
      return null;
    }
  }

  /// إنشاء تقرير المبيعات PDF
  static Future<Uint8List> _generateSalesReportPDF(
    List<Sale> sales,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'تقرير المبيعات',
                style:
                    pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                  'الفترة: ${DateHelper.formatDate(startDate)} - ${DateHelper.formatDate(endDate)}'),
              pw.SizedBox(height: 20),
              _buildSalesTable(sales),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// طباعة باركود المنتج
  static Future<Uint8List?> generateProductBarcode(Product product) async {
    try {
      return await _generateProductBarcodePDF(product);
    } catch (e, stack) {
      debugPrint('خطأ في إنشاء باركود المنتج: $e\n$stack');
      return null;
    }
  }

  /// إنشاء باركود المنتج PDF
  static Future<Uint8List> _generateProductBarcodePDF(Product product) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text(
                'باركود المنتج',
                style:
                    pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 20),
              pw.Text(product.name),
              pw.SizedBox(height: 20),
              if (product.barcode != null) pw.Text(product.barcode!),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// بناء جدول المبيعات
  static pw.Widget _buildSalesTable(List<Sale> sales) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            pw.Text('رقم الفاتورة',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('التاريخ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('العميل',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('المبلغ',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          ],
        ),
        ...sales.map((sale) => pw.TableRow(
              children: [
                pw.Text(sale.id?.toString() ?? ''),
                pw.Text(sale.date ?? ''),
                pw.Text(sale.customerName ?? ''),
                pw.Text(Formatters.formatCurrency(sale.total ?? 0)),
              ],
            )),
      ],
    );
  }

  /// بناء جدول المنتجات
  static pw.Widget _buildProductsTable(List<Product> products) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            pw.Text('الاسم',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('الفئة',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('الكمية',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('السعر',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          ],
        ),
        ...products.map((product) => pw.TableRow(
              children: [
                pw.Text(product.name),
                pw.Text(product.category ?? ''),
                pw.Text(product.quantity.toString()),
                pw.Text(Formatters.formatCurrency(product.salePrice ?? 0)),
              ],
            )),
      ],
    );
  }

  /// بناء جدول العملاء
  static pw.Widget _buildCustomersTable(List<Customer> customers) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        pw.TableRow(
          children: [
            pw.Text('الاسم',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('الهاتف',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            pw.Text('الرصيد',
                style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          ],
        ),
        ...customers.map((customer) => pw.TableRow(
              children: [
                pw.Text(customer.name ?? ''),
                pw.Text(customer.phone ?? ''),
                pw.Text(Formatters.formatCurrency(customer.balance ?? 0)),
              ],
            )),
      ],
    );
  }

  /// إنشاء PDF لفاتورة البيع
  static Future<Uint8List> _generateSaleInvoicePDF(
      Sale sale, List<Map<String, dynamic>> items) async {
    final pw.Document pdf = pw.Document();

    // تحميل الخط العربي
    final pw.Font font = await PdfGoogleFonts.cairoRegular();
    final pw.Font boldFont = await PdfGoogleFonts.cairoBold();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: <pw.Widget>[
              // رأس الفاتورة
              _buildInvoiceHeader(sale),
              pw.SizedBox(height: 20),

              // معلومات الفاتورة
              _buildSaleInvoiceInfo(sale),
              pw.SizedBox(height: 20),

              // جدول العناصر
              _buildItemsTable(items),
              pw.SizedBox(height: 20),

              // المجموع
              _buildInvoiceTotal(sale),

              pw.Spacer(),

              // تذييل الفاتورة
              _buildInvoiceFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// إنشاء PDF لقائمة المنتجات
  static Future<Uint8List> _generateProductsListPDF(
      List<Product> products) async {
    final pw.Document pdf = pw.Document();
    final pw.Font font = await PdfGoogleFonts.cairoRegular();
    final pw.Font boldFont = await PdfGoogleFonts.cairoBold();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(base: font, bold: boldFont),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: <pw.Widget>[
              pw.Text(
                'قائمة المنتجات',
                style: pw.TextStyle(font: boldFont, fontSize: 24),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                  'تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}'),
              pw.SizedBox(height: 20),
              _buildProductsTable(products),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// إنشاء PDF لقائمة العملاء
  static Future<Uint8List> _generateCustomersListPDF(
      List<Customer> customers) async {
    final pw.Document pdf = pw.Document();
    final pw.Font font = await PdfGoogleFonts.cairoRegular();
    final pw.Font boldFont = await PdfGoogleFonts.cairoBold();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(base: font, bold: boldFont),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: <pw.Widget>[
              pw.Text(
                'قائمة العملاء',
                style: pw.TextStyle(font: boldFont, fontSize: 24),
              ),
              pw.SizedBox(height: 20),
              pw.Text(
                  'تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}'),
              pw.SizedBox(height: 20),
              _buildCustomersTable(customers),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// بناء رأس الفاتورة
  static pw.Widget _buildInvoiceHeader(Sale sale) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: <pw.Widget>[
          pw.Text(
            AppConstants.appName,
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'فاتورة بيع',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات فاتورة البيع
  static pw.Widget _buildSaleInvoiceInfo(Sale sale) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: <pw.Widget>[
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: <pw.Widget>[
            pw.Text('رقم الفاتورة: ${sale.invoiceNumber ?? sale.id}'),
            pw.Text('التاريخ: ${sale.date ?? ''}'),
            pw.Text('طريقة الدفع: ${sale.paymentMethod ?? ''}'),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: <pw.Widget>[
            pw.Text('العميل: ${sale.customerName ?? 'عميل نقدي'}'),
            pw.Text('الحالة: ${sale.status ?? ''}'),
          ],
        ),
      ],
    );
  }

  /// بناء جدول العناصر
  static pw.Widget _buildItemsTable(List<Map<String, dynamic>> items) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: <pw.TableRow>[
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: <pw.Widget>[
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text('المنتج',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text('الكمية',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text('السعر',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text('المجموع',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ),
          ],
        ),
        // صفوف البيانات
        ...items.map((Map<String, dynamic> item) => pw.TableRow(
              children: <pw.Widget>[
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(item['productName'] ?? ''),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text('${item['quantity'] ?? 0}'),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(Formatters.formatCurrency(item['price'] ?? 0)),
                ),
                pw.Padding(
                  padding: const pw.EdgeInsets.all(8),
                  child: pw.Text(Formatters.formatCurrency(
                      (item['quantity'] ?? 0) * (item['price'] ?? 0))),
                ),
              ],
            )),
      ],
    );
  }

  /// بناء مجموع الفاتورة
  static pw.Widget _buildInvoiceTotal(Sale sale) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        border: pw.Border.all(),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: <pw.Widget>[
          pw.Text(
            'المجموع الإجمالي:',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.Text(
            Formatters.formatCurrency(sale.total ?? 0),
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green800,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة
  static pw.Widget _buildInvoiceFooter() {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(10),
      child: pw.Column(
        children: <pw.Widget>[
          pw.Text(
            'شكراً لتعاملكم معنا',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'تم إنشاء هذه الفاتورة بواسطة ${AppConstants.appName}',
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  /// إنشاء PDF لفاتورة الشراء
  static Future<Uint8List> _generatePurchaseInvoicePDF(
      Purchase purchase, List<Map<String, dynamic>> items) async {
    final pw.Document pdf = pw.Document();

    // تحميل الخط العربي
    final pw.Font font = await PdfGoogleFonts.cairoRegular();
    final pw.Font boldFont = await PdfGoogleFonts.cairoBold();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: <pw.Widget>[
              // رأس الفاتورة
              _buildPurchaseInvoiceHeader(),
              pw.SizedBox(height: 20),

              // معلومات الفاتورة
              _buildPurchaseInvoiceInfo(purchase),
              pw.SizedBox(height: 20),

              // جدول العناصر
              _buildItemsTable(items),
              pw.SizedBox(height: 20),

              // المجموع
              _buildPurchaseInvoiceTotal(purchase),

              pw.Spacer(),

              // تذييل الفاتورة
              _buildInvoiceFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// بناء رأس فاتورة الشراء
  static pw.Widget _buildPurchaseInvoiceHeader() {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.purple50,
        border: pw.Border.all(color: PdfColors.purple),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        children: <pw.Widget>[
          pw.Text(
            AppConstants.appName,
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.purple800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            'فاتورة شراء',
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء معلومات فاتورة الشراء
  static pw.Widget _buildPurchaseInvoiceInfo(Purchase purchase) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: <pw.Widget>[
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: <pw.Widget>[
            pw.Text('رقم الفاتورة: ${purchase.invoiceNumber ?? purchase.id}'),
            pw.Text('التاريخ: ${purchase.date ?? ''}'),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: <pw.Widget>[
            pw.Text('المورد: ${purchase.supplierName ?? ''}'),
            pw.Text('الحالة: ${purchase.status ?? ''}'),
          ],
        ),
      ],
    );
  }

  /// بناء مجموع فاتورة الشراء
  static pw.Widget _buildPurchaseInvoiceTotal(Purchase purchase) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        border: pw.Border.all(),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: <pw.Widget>[
          pw.Text(
            'المجموع الإجمالي:',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.Text(
            Formatters.formatCurrency(purchase.total ?? 0),
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.purple800,
            ),
          ),
        ],
      ),
    );
  }

  /// إنشاء HTML لفاتورة البيع (للاحتياط)
  static String _generateSaleInvoiceHTML(
      Sale sale, List<Map<String, dynamic>> items) {
    final double totalAmount = sale.total ?? 0;
    final double itemsTotal = items.fold<double>(
        0,
        (double sum, Map<String, dynamic> item) =>
            sum + ((item['quantity'] ?? 0) * (item['price'] ?? 0)));

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة بيع</title>
    <style>
        ${_getCommonCSS()}
        .invoice-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .total-section {
            text-align: left;
            margin-top: 20px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .grand-total {
            font-size: 18px;
            font-weight: bold;
            border-top: 2px solid #333;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>فاتورة بيع</h2>
        </div>
        
        <div class="invoice-info">
            <div>
                <p><strong>رقم الفاتورة:</strong> ${sale.invoiceNumber ?? sale.id}</p>
                <p><strong>التاريخ:</strong> ${sale.date ?? ''}</p>
                <p><strong>طريقة الدفع:</strong> ${Formatters.formatPaymentMethod(sale.paymentMethod ?? '')}</p>
            </div>
            <div>
                <p><strong>العميل:</strong> ${sale.customerName ?? 'عميل نقدي'}</p>
                <p><strong>الحالة:</strong> ${_formatStatus(sale.status ?? '')}</p>
            </div>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((Map<String, dynamic> item) => '''
                <tr>
                    <td>${item['productName'] ?? ''}</td>
                    <td>${item['quantity'] ?? 0}</td>
                    <td>${Formatters.formatCurrency(item['price'] ?? 0)}</td>
                    <td>${Formatters.formatCurrency((item['quantity'] ?? 0) * (item['price'] ?? 0))}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="total-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span>${Formatters.formatCurrency(itemsTotal)}</span>
            </div>
            <div class="total-row grand-total">
                <span>المجموع الإجمالي:</span>
                <span>${Formatters.formatCurrency(totalAmount)}</span>
            </div>
        </div>
        
        ${sale.notes?.isNotEmpty == true ? '''
        <div class="notes">
            <h3>ملاحظات:</h3>
            <p>${sale.notes}</p>
        </div>
        ''' : ''}
        
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لفاتورة الشراء
  static String _generatePurchaseInvoiceHTML(
      Purchase purchase, List<Map<String, dynamic>> items) {
    final double totalAmount = purchase.total ?? 0;
    final double itemsTotal = items.fold<double>(
        0,
        (double sum, Map<String, dynamic> item) =>
            sum + ((item['quantity'] ?? 0) * (item['price'] ?? 0)));

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة شراء</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>فاتورة شراء</h2>
        </div>
        
        <div class="invoice-info">
            <div>
                <p><strong>رقم الفاتورة:</strong> ${purchase.invoiceNumber ?? purchase.id}</p>
                <p><strong>التاريخ:</strong> ${purchase.date ?? ''}</p>
            </div>
            <div>
                <p><strong>المورد:</strong> ${purchase.supplierName ?? ''}</p>
                <p><strong>الحالة:</strong> ${_formatStatus(purchase.status ?? '')}</p>
            </div>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                ${items.map((Map<String, dynamic> item) => '''
                <tr>
                    <td>${item['productName'] ?? ''}</td>
                    <td>${item['quantity'] ?? 0}</td>
                    <td>${Formatters.formatCurrency(item['price'] ?? 0)}</td>
                    <td>${Formatters.formatCurrency((item['quantity'] ?? 0) * (item['price'] ?? 0))}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="total-section">
            <div class="total-row grand-total">
                <span>المجموع الإجمالي:</span>
                <span>${Formatters.formatCurrency(totalAmount)}</span>
            </div>
        </div>
        
        <div class="footer">
            <p>تم إنشاء هذه الفاتورة بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لقائمة المنتجات
  static String _generateProductsListHTML(List<Product> products) {
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المنتجات</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>قائمة المنتجات</h2>
            <p>تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}</p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الفئة</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>سعر الشراء</th>
                    <th>سعر البيع</th>
                    <th>الحد الأدنى</th>
                </tr>
            </thead>
            <tbody>
                ${products.map((Product product) => '''
                <tr>
                    <td>${product.name ?? ''}</td>
                    <td>${product.category ?? ''}</td>
                    <td>${product.quantity ?? 0}</td>
                    <td>${product.unit ?? ''}</td>
                    <td>${Formatters.formatCurrency(product.purchasePrice ?? 0)}</td>
                    <td>${Formatters.formatCurrency(product.salePrice ?? 0)}</td>
                    <td>${product.minLevel ?? 0}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="footer">
            <p>إجمالي المنتجات: ${products.length}</p>
            <p>تم إنشاء هذا التقرير بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لقائمة العملاء
  static String _generateCustomersListHTML(List<Customer> customers) {
    final double totalBalance = customers.fold<double>(
        0, (double sum, Customer customer) => sum + (customer.balance ?? 0));

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة العملاء</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>قائمة العملاء</h2>
            <p>تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}</p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>البريد الإلكتروني</th>
                    <th>العنوان</th>
                    <th>الرصيد</th>
                </tr>
            </thead>
            <tbody>
                ${customers.map((Customer customer) => '''
                <tr>
                    <td>${customer.name ?? ''}</td>
                    <td>${customer.phone ?? ''}</td>
                    <td>${customer.email ?? ''}</td>
                    <td>${customer.address ?? ''}</td>
                    <td>${Formatters.formatCurrency(customer.balance ?? 0)}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="footer">
            <p>إجمالي العملاء: ${customers.length}</p>
            <p>إجمالي الأرصدة: ${Formatters.formatCurrency(totalBalance)}</p>
            <p>تم إنشاء هذا التقرير بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لتقرير المبيعات
  static String _generateSalesReportHTML(
      List<Sale> sales, DateTime startDate, DateTime endDate) {
    final double totalAmount = sales.fold<double>(
        0, (double sum, Sale sale) => sum + (sale.total ?? 0));
    final int completedSales =
        sales.where((Sale sale) => sale.status == 'completed').length;

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المبيعات</title>
    <style>
        ${_getCommonCSS()}
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <h1>${AppConstants.appName}</h1>
            <h2>تقرير المبيعات</h2>
            <p>من ${DateHelper.formatDate(startDate)} إلى ${DateHelper.formatDate(endDate)}</p>
            <p>تاريخ الطباعة: ${DateHelper.formatDateTime(DateTime.now())}</p>
        </div>
        
        <div class="summary">
            <h3>ملخص التقرير</h3>
            <p>إجمالي المبيعات: ${sales.length}</p>
            <p>المبيعات المكتملة: $completedSales</p>
            <p>إجمالي المبلغ: ${Formatters.formatCurrency(totalAmount)}</p>
        </div>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>التاريخ</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                ${sales.map((Sale sale) => '''
                <tr>
                    <td>${sale.invoiceNumber ?? sale.id}</td>
                    <td>${sale.date ?? ''}</td>
                    <td>${sale.customerName ?? 'عميل نقدي'}</td>
                    <td>${Formatters.formatCurrency(sale.total ?? 0)}</td>
                    <td>${Formatters.formatPaymentMethod(sale.paymentMethod ?? '')}</td>
                    <td>${_formatStatus(sale.status ?? '')}</td>
                </tr>
                ''').join('')}
            </tbody>
        </table>
        
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة ${AppConstants.appName}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// إنشاء HTML لباركود المنتج
  static String _generateProductBarcodeHTML(Product product) {
    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>باركود المنتج</title>
    <style>
        ${_getCommonCSS()}
        .barcode-container {
            text-align: center;
            margin: 50px 0;
        }
        .barcode {
            font-family: 'Libre Barcode 128', monospace;
            font-size: 48px;
            margin: 20px 0;
        }
        .product-info {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="barcode-container">
            <div class="product-info">
                <h2>${product.name ?? ''}</h2>
                <p>الفئة: ${product.category ?? ''}</p>
                <p>السعر: ${Formatters.formatCurrency(product.salePrice ?? 0)}</p>
            </div>
            
            <div class="barcode">
                ${product.barcode ?? ''}
            </div>
            
            <p>${product.barcode ?? ''}</p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// الحصول على CSS المشترك
  static String _getCommonCSS() {
    return '''
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1, h2, h3 {
            margin-bottom: 10px;
        }
        
        p {
            margin-bottom: 5px;
        }
        
        .summary {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .notes {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 10px;
            }
        }
    ''';
  }

  /// تنسيق الحالة
  static String _formatStatus(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'معلقة';
      case 'cancelled':
        return 'ملغية';
      case 'draft':
        return 'مسودة';
      default:
        return status;
    }
  }

  /// طباعة PDF
  static Future<void> printPDF(Uint8List pdfBytes) async {
    try {
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
      );
    } catch (e, stack) {
      debugPrint('خطأ في طباعة PDF: $e\n$stack');
    }
  }

  /// مشاركة PDF
  static Future<void> sharePDF(Uint8List pdfBytes, String fileName) async {
    try {
      final Directory tempDir = await getTemporaryDirectory();
      final File file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(pdfBytes);

      await Share.shareXFiles(
        <XFile>[XFile(file.path)],
        text: 'مشاركة ملف PDF من ${AppConstants.appName}',
      );
    } catch (e, stack) {
      debugPrint('خطأ في مشاركة PDF: $e\n$stack');
    }
  }

  /// حفظ PDF
  static Future<String?> savePDF(Uint8List pdfBytes, String fileName) async {
    try {
      final Directory? downloadsDir = await getDownloadsDirectory();
      final Directory appDir =
          downloadsDir ?? await getApplicationDocumentsDirectory();

      final String fullPath = '${appDir.path}/$fileName';
      final File file = File(fullPath);
      await file.writeAsBytes(pdfBytes);

      return fullPath;
    } catch (e, stack) {
      debugPrint('خطأ في حفظ PDF: $e\n$stack');
      return null;
    }
  }

  /// طباعة HTML مباشرة (للويب)
  static void printHTML(String html) {
    if (kIsWeb) {
      // للويب - استخدام window.print()
      // يمكن تنفيذ هذا لاحقاً حسب الحاجة
      debugPrint('طباعة HTML للويب غير مدعومة حالياً');
    }
  }
}
