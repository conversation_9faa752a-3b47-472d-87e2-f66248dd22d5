/// ملف التطبيق الرئيسي لأسامة ماركت - نظام إدارة المخزون الذكي
///
/// يحتوي على:
/// - إعداد قاعدة البيانات
/// - إعداد Workmanager للمهام الخلفية
/// - إعداد Providers للحالة العامة
/// - إعداد التوجيه والثيم
///
/// المطور: فريق أسامة ماركت
/// التاريخ: 2024
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:nested/nested.dart';

import 'package:provider/provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:workmanager/workmanager.dart';

import 'core/app_router.dart';
import 'generated/app_localizations.dart';
import 'providers/analytics_provider.dart';
import 'providers/backup_provider.dart';
import 'providers/customer_provider.dart';

import 'providers/product_provider.dart';
import 'providers/purchase_provider.dart';
import 'providers/sale_provider.dart';
import 'providers/supplier_provider.dart';
import 'services/backup_service.dart';
import 'services/database_service.dart';
import 'utils/app_theme.dart';

/// Workmanager callback for background tasks
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager()
      .executeTask((String task, Map<String, dynamic>? inputData) async {
    try {
      print('🔄 Executing background task: $task');

      switch (task) {
        case 'autoBackupTask':
          await _performAutoBackup(inputData);
          break;
        default:
          print('❌ Unknown task: $task');
          return Future.value(false);
      }

      print('✅ Background task completed: $task');
      return Future.value(true);
    } catch (e) {
      print('❌ Background task failed: $task - $e');
      return Future.value(false);
    }
  });
}

/// Perform automatic backup
Future<void> _performAutoBackup(Map<String, dynamic>? inputData) async {
  try {
    final BackupService backupService = BackupService();

    // Create local backup
    await backupService.createLocalDatabaseBackup();
    print('✅ Auto backup: Local backup created');

    // Upload to cloud if enabled
    final bool backupToCloud = inputData?['backup_to_cloud'] ?? false;
    if (backupToCloud && backupService.isSignedIn) {
      await backupService.uploadDatabaseToGoogleDrive();
      print('✅ Auto backup: Cloud backup uploaded');
    }

    // Clean old backups
    await backupService.cleanOldLocalDatabaseBackups();
    print('✅ Auto backup: Old backups cleaned');
  } catch (e) {
    print('❌ Auto backup failed: $e');
    rethrow;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Workmanager
  try {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );
    print('✅ Workmanager initialized successfully');
  } catch (e) {
    print('❌ Workmanager initialization error: $e');
  }

  // Initialize database
  try {
    // Initialize database factory for web
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    }

    await DatabaseService.instance.database;
    print('✅ Database initialized successfully');
  } catch (e) {
    print('❌ Database initialization error: $e');
  }

  runApp(const MyApp());
}

/// الكلاس الرئيسي للتطبيق
///
/// يحتوي على:
/// - إعداد MultiProvider لجميع مقدمي الحالة
/// - إعداد MaterialApp.router مع نظام التوجيه الموحد
/// - إعداد الثيم الفاتح والداكن
/// - إعداد اتجاه النص العربي (RTL)
class MyApp extends StatelessWidget {
  /// منشئ الكلاس الرئيسي للتطبيق
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: <SingleChildWidget>[
        ChangeNotifierProvider(
          create: (_) => ProductProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SaleProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => PurchaseProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => CustomerProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => SupplierProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) => AnalyticsProvider(),
        ),
        ChangeNotifierProvider(
          create: (_) {
            final BackupProvider provider = BackupProvider();
            // تأخير التهيئة لتجنب مشاكل الأداء
            Future.delayed(const Duration(seconds: 1), () {
              provider.initialize();
            });
            return provider;
          },
        ),
      ],
      child: MaterialApp.router(
        title: 'أسامة ماركت - إدارة ذكية لمتجرك',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        themeMode: ThemeMode.light,
        routerConfig: AppRouter.router,
        localizationsDelegates: const <LocalizationsDelegate<dynamic>>[
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: AppLocalizations.supportedLocales,
        locale: const Locale('ar', 'SA'),
        builder: (BuildContext context, Widget? child) {
          return Directionality(
            textDirection: TextDirection.rtl,
            child: child ?? const SizedBox(),
          );
        },
      ),
    );
  }
}
