import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/supplier.dart';
import '../../providers/supplier_provider.dart';
import '../../config/app_colors.dart';
import '../../config/app_dimensions.dart';
import '../../config/app_styles.dart';

/// حوار اختيار المورد
class SelectSupplierDialog extends StatefulWidget {
  final Supplier? selectedSupplier;
  final Function(Supplier) onSupplierSelected;

  const SelectSupplierDialog({
    super.key,
    this.selectedSupplier,
    required this.onSupplierSelected,
  });

  @override
  State<SelectSupplierDialog> createState() => _SelectSupplierDialogState();
}

class _SelectSupplierDialogState extends State<SelectSupplierDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Supplier> _filteredSuppliers = <Supplier>[];
  Supplier? _selectedSupplier;

  @override
  void initState() {
    super.initState();
    _selectedSupplier = widget.selectedSupplier;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSuppliers();
    });
  }

  void _loadSuppliers() {
    final SupplierProvider supplierProvider = context.read<SupplierProvider>();
    supplierProvider.fetchSuppliers();
    _filterSuppliers('');
  }

  void _filterSuppliers(String query) {
    final SupplierProvider supplierProvider = context.read<SupplierProvider>();
    final List<Supplier> allSuppliers = supplierProvider.suppliers;

    setState(() {
      if (query.isEmpty) {
        _filteredSuppliers = allSuppliers;
      } else {
        _filteredSuppliers = allSuppliers.where((Supplier supplier) {
          return (supplier.name?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (supplier.phone?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (supplier.email?.toLowerCase().contains(query.toLowerCase()) ??
                  false);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            children: <Widget>[
              _buildHeader(),
              const SizedBox(height: AppDimensions.paddingM),
              _buildSearchField(),
              const SizedBox(height: AppDimensions.paddingM),
              Expanded(child: _buildSuppliersList()),
              const SizedBox(height: AppDimensions.paddingM),
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: <Widget>[
        const Icon(
          Icons.business,
          color: AppColors.primary,
          size: 24,
        ),
        const SizedBox(width: AppDimensions.paddingS),
        const Expanded(
          child: Text(
            'اختيار المورد',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      decoration: const InputDecoration(
        labelText: 'البحث عن مورد',
        hintText: 'ادخل اسم المورد أو رقم الهاتف',
        prefixIcon: Icon(Icons.search),
        border: OutlineInputBorder(),
      ),
      onChanged: _filterSuppliers,
    );
  }

  Widget _buildSuppliersList() {
    return Consumer<SupplierProvider>(
      builder:
          (BuildContext context, SupplierProvider provider, Widget? child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Icon(Icons.error, size: 64, color: AppColors.error),
                const SizedBox(height: AppDimensions.paddingM),
                Text(
                  'خطأ في تحميل الموردين: ${provider.error}',
                  style: AppStyles.bodyMedium.copyWith(color: AppColors.error),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                ElevatedButton(
                  onPressed: _loadSuppliers,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (_filteredSuppliers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Icon(Icons.business_outlined,
                    size: 64, color: AppColors.textSecondary),
                const SizedBox(height: AppDimensions.paddingM),
                Text(
                  _searchController.text.isEmpty
                      ? 'لا توجد موردين'
                      : 'لا توجد نتائج للبحث',
                  style: AppStyles.bodyMedium
                      .copyWith(color: AppColors.textSecondary),
                ),
                if (_searchController.text.isEmpty) ...[
                  const SizedBox(height: AppDimensions.paddingM),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      // يمكن إضافة التنقل لشاشة إضافة مورد جديد
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة مورد جديد'),
                  ),
                ],
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: _filteredSuppliers.length,
          itemBuilder: (BuildContext context, int index) {
            final Supplier supplier = _filteredSuppliers[index];
            final bool isSelected = _selectedSupplier?.id == supplier.id;

            return Card(
              margin:
                  const EdgeInsets.symmetric(vertical: AppDimensions.paddingXS),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor:
                      isSelected ? AppColors.primary : AppColors.surface,
                  child: Text(
                    (supplier.name?.isNotEmpty == true)
                        ? supplier.name![0].toUpperCase()
                        : '؟',
                    style: TextStyle(
                      color: isSelected
                          ? AppColors.textOnPrimary
                          : AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                title: Text(
                  supplier.name ?? 'مورد غير محدد',
                  style: AppStyles.titleMedium.copyWith(
                    color:
                        isSelected ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    if (supplier.phone != null && supplier.phone!.isNotEmpty)
                      Text('📞 ${supplier.phone}'),
                    if (supplier.email != null && supplier.email!.isNotEmpty)
                      Text('📧 ${supplier.email}'),
                    if (supplier.category != null &&
                        supplier.category!.isNotEmpty)
                      Text('🏷️ ${supplier.category}'),
                  ],
                ),
                trailing: isSelected
                    ? const Icon(Icons.check_circle, color: AppColors.primary)
                    : null,
                selected: isSelected,
                onTap: () {
                  setState(() {
                    _selectedSupplier = supplier;
                  });
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildActions() {
    return Row(
      children: <Widget>[
        Expanded(
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: ElevatedButton(
            onPressed: _selectedSupplier != null
                ? () {
                    widget.onSupplierSelected(_selectedSupplier!);
                    Navigator.pop(context);
                  }
                : null,
            child: const Text('اختيار'),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
