import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../widgets/enhanced_confirmation_dialog.dart';

/// مدير التنقل الذكي للتعامل مع زر الرجوع
class NavigationManager {
  static DateTime? _lastBackPressed;

  /// التعامل مع زر الرجوع للهاتف
  static Future<bool> handleBackPress(BuildContext context) async {
    final String currentRoute = GoRouterState.of(context).uri.path;

    // إذا كنا في شاشة فرعية أو تحت فرعية
    if (currentRoute != '/' && currentRoute != '/dashboard') {
      if (GoRouter.of(context).canPop()) {
        GoRouter.of(context).pop();
        return false; // منع الخروج من التطبيق، تم التراجع خطوة
      } else {
        // لا يوجد شيء في المكدس، انتقل للشاشة الرئيسية
        context.go('/');
        return false; // منع الخروج من التطبيق، تم الانتقال للرئيسية
      }
    }
    // إذا كنا في الشاشة الرئيسية الأساسية (/, /dashboard)
    else {
      // استخدام المنطق الحالي لحوار تأكيد الخروج
      return await _handleHomeBackPress(context);
    }
  }

  /// التعامل مع الرجوع في الصفحة الرئيسية
  static Future<bool> _handleHomeBackPress(BuildContext context) async {
    // تنفيذ ضغطة مزدوجة للخروج إذا تم تفعيلها
    if (_isDoubleBackEnabled()) {
      return _handleDoubleBackPress(context);
    }

    // عرض حوار تأكيد الخروج
    final bool? result = await showDialog<bool>(
      context: context,
      barrierDismissible: true, // يمكن للمستخدم إلغاء الحوار بالضغط خارجه
      builder: (BuildContext context) => const EnhancedConfirmationDialog(
        title: 'تأكيد الخروج',
        message: 'هل تريد الخروج من التطبيق؟',
        confirmText: 'خروج',
        cancelText: 'إلغاء',
        isDanger: true, // للإشارة إلى أن هذا إجراء خطير
      ),
    );
    final bool shouldExit = result ?? false;

    if (shouldExit) {
      // إضافة تأثير اهتزاز خفيف عند الخروج
      HapticFeedback.lightImpact();
    }
    return shouldExit; // السماح بالخروج إذا كانت النتيجة true
  }

  /// التحقق من تفعيل خاصية الضغط المزدوج للخروج
  static bool _isDoubleBackEnabled() {
    // يمكن تحويلها لإعداد في المستقبل
    return false;
  }

  /// التعامل مع الضغط المزدوج للخروج
  static bool _handleDoubleBackPress(BuildContext context) {
    final DateTime now = DateTime.now();

    if (_lastBackPressed == null ||
        now.difference(_lastBackPressed!) > const Duration(seconds: 2)) {
      _lastBackPressed = now;

      // إظهار رسالة للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اضغط مرة أخرى للخروج'),
          duration: Duration(seconds: 2),
        ),
      );

      return false; // منع الخروج في الضغطة الأولى
    }

    // إضافة تأثير اهتزاز
    HapticFeedback.lightImpact();
    return true; // السماح بالخروج في الضغطة الثانية
  }

  /// التنقل الآمن مع إغلاق الـ drawer
  static void navigateAndCloseDrawer(BuildContext context, String route) {
    // إغلاق الـ drawer إذا كان مفتوحاً
    if (Scaffold.of(context).isDrawerOpen) {
      Scaffold.of(context).closeDrawer();
    }

    // التنقل للصفحة
    context.go(route);
  }

  /// التنقل مع push
  static void navigateTo(BuildContext context, String route) {
    context.push(route);
  }

  /// الرجوع للصفحة السابقة
  static void goBack(BuildContext context) {
    if (GoRouter.of(context).canPop()) {
      GoRouter.of(context).pop();
    } else {
      context.go('/');
    }
  }

  /// الرجوع للصفحة الرئيسية
  static void goHome(BuildContext context) {
    context.go('/');
  }

  /// التحقق من كون الصفحة الحالية هي الرئيسية
  static bool isHomePage(BuildContext context) {
    final String currentRoute = GoRouterState.of(context).uri.path;
    return currentRoute == '/' || currentRoute == '/dashboard';
  }

  /// الحصول على المسار الحالي
  static String getCurrentRoute(BuildContext context) {
    return GoRouterState.of(context).uri.path;
  }

  /// التحقق من إمكانية الرجوع
  static bool canGoBack(BuildContext context) {
    return GoRouter.of(context).canPop();
  }
}

/// Widget للتعامل مع زر الرجوع للهاتف
class BackButtonHandler extends StatelessWidget {
  final Widget child;
  final bool enableBackButton;
  final Future<bool> Function()? onBackPressed;

  const BackButtonHandler({
    super.key,
    required this.child,
    this.enableBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    if (!enableBackButton) {
      return child;
    }

    return PopScope(
      canPop: false, // نتحكم نحن في السلوك
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (!didPop) {
          // إذا لم يتم التراجع تلقائيًا (وهو كذلك لأن canPop: false)
          bool shouldPopSystemLevel =
              false; // هل يجب أن نسمح للـ PopScope بإغلاق التطبيق

          if (onBackPressed != null) {
            shouldPopSystemLevel = await onBackPressed!();
          } else {
            shouldPopSystemLevel =
                await NavigationManager.handleBackPress(context);
          }

          if (shouldPopSystemLevel) {
            // إذا كانت handleBackPress تُرجع true (يعني الخروج من الرئيسية بعد تأكيد)
            await SystemNavigator.pop(); // أغلق التطبيق
          }
        }
      },
      child: child,
    );
  }
}

/// Mixin للشاشات التي تحتاج للتعامل مع زر الرجوع
mixin BackButtonMixin<T extends StatefulWidget> on State<T> {
  /// التعامل مع زر الرجوع
  Future<bool> onBackPressed() async {
    return await NavigationManager.handleBackPress(context);
  }

  /// بناء الشاشة مع معالج زر الرجوع
  Widget buildWithBackHandler(Widget child) {
    return BackButtonHandler(
      onBackPressed: onBackPressed,
      child: child,
    );
  }
}
