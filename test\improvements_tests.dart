/// اختبارات التحسينات الإضافية لتطبيق أسامة ماركت
///
/// تتضمن اختبارات للتحسينات التالية:
/// 1. التدويل (i18n)
/// 2. إدارة الاعتماديات (Dependency Injection)
/// 3. فصل منطق الأعمال عن BuildContext
/// 4. الموديلات مع final fields
///
/// المطور: فريق أسامة ماركت
/// التاريخ: 2024
library improvements_tests;

import 'package:flutter_test/flutter_test.dart';
import 'package:inventory_management_app/core/dependency_injection.dart';
import 'package:inventory_management_app/core/ui_helpers.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/providers/product_provider.dart';
import 'package:inventory_management_app/services/product_service.dart';

void main() {
  group('🌍 اختبارات التدويل (Internationalization)', () {
    test('يجب أن تكون ملفات الترجمة متوفرة', () {
      // هذا اختبار أساسي للتأكد من وجود ملفات الترجمة
      // في التطبيق الحقيقي، يمكن اختبار AppLocalizations
      expect(true, isTrue); // placeholder
    });

    test('يجب أن تدعم اللغة العربية والإنجليزية', () {
      // اختبار دعم اللغات المختلفة
      const List<String> supportedLanguages = <String>['ar', 'en'];
      expect(supportedLanguages.contains('ar'), isTrue);
      expect(supportedLanguages.contains('en'), isTrue);
    });
  });

  group('🔧 اختبارات إدارة الاعتماديات (Dependency Injection)', () {
    setUp(() async {
      // إعادة تعيين الاعتماديات قبل كل اختبار
      await resetDependencyInjection();
    });

    test('يجب أن تتم تهيئة الاعتماديات بنجاح', () async {
      await setupDependencyInjection();
      expect(isDependencyInjectionReady, isTrue);
    });

    test('يجب أن يتم حقن الخدمات في المزودات', () async {
      await setupDependencyInjection();
      
      // إنشاء مزود مع حقن الاعتماديات
      final ProductService productService = getService<ProductService>();
      final ProductProvider provider = ProductProvider.withService(productService);
      
      expect(provider, isNotNull);
      expect(provider.products, isEmpty);
    });

    test('يجب أن تعمل الخدمات كـ Singletons', () async {
      await setupDependencyInjection();
      
      final ProductService service1 = getService<ProductService>();
      final ProductService service2 = getService<ProductService>();
      
      // يجب أن يكونا نفس المثيل
      expect(identical(service1, service2), isTrue);
    });

    test('يجب أن تعمل المزودات كـ Factories', () async {
      await setupDependencyInjection();
      
      final ProductProvider provider1 = createProvider<ProductProvider>();
      final ProductProvider provider2 = createProvider<ProductProvider>();
      
      // يجب أن يكونا مثيلين مختلفين
      expect(identical(provider1, provider2), isFalse);
    });
  });

  group('🎨 اختبارات فصل منطق الأعمال عن BuildContext', () {
    setUp(() {
      // إلغاء تسجيل callbacks قبل كل اختبار
      UICallbacks.unregister();
    });

    test('يجب أن تعمل UICallbacks بدون BuildContext', () {
      bool successCalled = false;
      bool errorCalled = false;

      // تسجيل callbacks وهمية
      UICallbacks.register(
        showSuccess: (String message) {
          successCalled = true;
          expect(message, equals('نجح الاختبار'));
        },
        showError: (String message) {
          errorCalled = true;
          expect(message, equals('فشل الاختبار'));
        },
      );

      // اختبار استدعاء callbacks
      UICallbacks.showSuccess('نجح الاختبار');
      UICallbacks.showError('فشل الاختبار');

      expect(successCalled, isTrue);
      expect(errorCalled, isTrue);
    });

    test('يجب أن تعمل OperationResult بشكل صحيح', () {
      // اختبار نتيجة ناجحة
      final OperationResult<String> successResult = OperationResult.success(
        data: 'بيانات الاختبار',
        message: 'نجح الاختبار',
      );

      expect(successResult.success, isTrue);
      expect(successResult.data, equals('بيانات الاختبار'));
      expect(successResult.message, equals('نجح الاختبار'));

      // اختبار نتيجة فاشلة
      final OperationResult<String> failureResult = OperationResult.failure(
        errorMessage: 'فشل الاختبار',
      );

      expect(failureResult.success, isFalse);
      expect(failureResult.data, isNull);
      expect(failureResult.errorMessage, equals('فشل الاختبار'));
    });

    test('يجب أن يعمل OperationHelper مع العمليات الناجحة', () async {
      bool successCalled = false;

      UICallbacks.register(
        showSuccess: (String message) {
          successCalled = true;
          expect(message, equals('تم بنجاح'));
        },
      );

      final OperationResult<String> result = await OperationHelper.execute(
        () async => 'نتيجة الاختبار',
        successMessage: 'تم بنجاح',
      );

      expect(result.success, isTrue);
      expect(result.data, equals('نتيجة الاختبار'));
      expect(successCalled, isTrue);
    });

    test('يجب أن يعمل OperationHelper مع العمليات الفاشلة', () async {
      bool errorCalled = false;

      UICallbacks.register(
        showError: (String message) {
          errorCalled = true;
          expect(message, contains('خطأ في الاختبار'));
        },
      );

      final OperationResult<String> result = await OperationHelper.execute(
        () async => throw Exception('خطأ في الاختبار'),
        errorPrefix: 'فشل العملية',
      );

      expect(result.success, isFalse);
      expect(result.data, isNull);
      expect(errorCalled, isTrue);
    });
  });

  group('🔒 اختبارات الموديلات مع final fields', () {
    test('يجب أن تكون حقول Product غير قابلة للتغيير', () {
      final Product product = Product(
        name: 'منتج اختبار',
        description: 'وصف الاختبار',
        price: 10.0,
        warehouseQuantity: 100,
        storeQuantity: 50,
      );

      expect(product.name, equals('منتج اختبار'));
      expect(product.description, equals('وصف الاختبار'));
      expect(product.price, equals(10.0));
      expect(product.warehouseQuantity, equals(100));
      expect(product.storeQuantity, equals(50));

      // لا يمكن تغيير القيم مباشرة لأنها final
      // product.name = 'اسم جديد'; // هذا سيسبب خطأ في التجميع
    });

    test('يجب أن تعمل دالة copyWith بشكل صحيح', () {
      final Product originalProduct = Product(
        name: 'منتج أصلي',
        description: 'وصف أصلي',
        price: 10.0,
        warehouseQuantity: 100,
      );

      final Product updatedProduct = originalProduct.copyWith(
        name: 'منتج محدث',
        price: 15.0,
      );

      // التحقق من أن المنتج الأصلي لم يتغير
      expect(originalProduct.name, equals('منتج أصلي'));
      expect(originalProduct.price, equals(10.0));

      // التحقق من أن المنتج الجديد يحتوي على التحديثات
      expect(updatedProduct.name, equals('منتج محدث'));
      expect(updatedProduct.price, equals(15.0));
      expect(updatedProduct.description, equals('وصف أصلي')); // لم يتغير
      expect(updatedProduct.warehouseQuantity, equals(100)); // لم يتغير
    });

    test('يجب أن تعمل دوال toMap و fromMap بشكل صحيح', () {
      final Product originalProduct = Product(
        id: 1,
        name: 'منتج اختبار',
        description: 'وصف اختبار',
        price: 10.0,
        warehouseQuantity: 100,
        storeQuantity: 50,
        createdAt: DateTime(2024, 1, 1),
      );

      // تحويل إلى Map
      final Map<String, dynamic> productMap = originalProduct.toMap();
      expect(productMap['name'], equals('منتج اختبار'));
      expect(productMap['price'], equals(10.0));

      // تحويل من Map
      final Product restoredProduct = Product.fromMap(productMap);
      expect(restoredProduct.name, equals(originalProduct.name));
      expect(restoredProduct.price, equals(originalProduct.price));
      expect(restoredProduct.warehouseQuantity, equals(originalProduct.warehouseQuantity));
    });
  });

  group('🧪 اختبارات التكامل للتحسينات', () {
    test('يجب أن تعمل جميع التحسينات معاً', () async {
      // تهيئة إدارة الاعتماديات
      await setupDependencyInjection();

      // تسجيل UI callbacks
      bool operationCompleted = false;
      UICallbacks.register(
        showSuccess: (String message) {
          operationCompleted = true;
        },
      );

      // إنشاء منتج immutable
      final Product product = Product(
        name: 'منتج تكامل',
        description: 'اختبار التكامل',
        price: 20.0,
      );

      // استخدام المزود مع حقن الاعتماديات
      final ProductProvider provider = createProvider<ProductProvider>();

      // تنفيذ عملية مع معالجة الأخطاء
      final OperationResult<Product> result = await OperationHelper.execute(
        () async {
          // محاكاة عملية ناجحة
          return product.copyWith(id: 1);
        },
        successMessage: 'تم إنشاء المنتج بنجاح',
      );

      expect(result.success, isTrue);
      expect(result.data?.name, equals('منتج تكامل'));
      expect(operationCompleted, isTrue);
      expect(provider, isNotNull);
    });
  });
}
