import 'package:flutter/material.dart';
import '../../utils/navigation_manager.dart';
import '../../widgets/conditional_bottom_nav.dart';
import '../../widgets/modern_drawer.dart';
import '../../config/app_colors.dart';

/// شاشة التقارير والإحصائيات
class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: MainScreenWrapper(
          title: 'التقارير والإحصائيات',
          needsDrawer: false, // زر القائمة فقط في الشاشة الرئيسية
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _exportCurrentReport,
              tooltip: 'تصدير التقرير',
            ),
            IconButton(
              icon: const Icon(Icons.print),
              onPressed: _printCurrentReport,
              tooltip: 'طباعة التقرير',
            ),
          ],
          child: Scaffold(
            drawer: const ModernDrawer(),
            body: Container(
              color: AppColors.background,
              child: Column(
                children: <Widget>[
                  _buildTabBar(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: <Widget>[
                        _buildSalesReports(),
                        _buildInventoryReports(),
                        _buildFinancialReports(),
                        _buildCustomerReports(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        tabs: const <Widget>[
          Tab(text: 'المبيعات'),
          Tab(text: 'المخزون'),
          Tab(text: 'المالية'),
          Tab(text: 'العملاء'),
        ],
      ),
    );
  }

  Widget _buildSalesReports() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.analytics,
            size: 64,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'تقرير المبيعات',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم إضافة تفاصيل المبيعات قريباً',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryReports() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.inventory_2,
            size: 64,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'تقرير المخزون',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم إضافة تفاصيل المخزون قريباً',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialReports() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.account_balance,
            size: 64,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'التقرير المالي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم إضافة التفاصيل المالية قريباً',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerReports() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Icons.people,
            size: 64,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'تقرير العملاء',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم إضافة تفاصيل العملاء قريباً',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _exportCurrentReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة التصدير قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _printCurrentReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم إضافة ميزة الطباعة قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
