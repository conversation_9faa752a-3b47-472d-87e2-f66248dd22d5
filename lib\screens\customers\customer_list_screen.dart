import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:fast_contacts/fast_contacts.dart';
import '../../config/app_colors.dart';
import '../../config/app_styles.dart';
import '../../config/app_dimensions.dart';

import '../../widgets/enhanced_confirmation_dialog.dart';
import '../../widgets/modern_drawer.dart';
import '../../utils/navigation_manager.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/formatters.dart';
import '../../providers/customer_provider.dart';
import '../../models/customer.dart';
import '../dialogs/import_contacts_dialog.dart';

/// شاشة قائمة العملاء
class CustomerListScreen extends StatefulWidget {
  const CustomerListScreen({super.key});

  @override
  State<CustomerListScreen> createState() => _CustomerListScreenState();
}

class _CustomerListScreenState extends State<CustomerListScreen> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _sortAscending = true;
  final bool _showAddForm = false;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  void _loadCustomers() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CustomerProvider>().loadCustomers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      child: Scaffold(
        drawer: const ModernDrawer(),
        appBar: AppBar(
          title: const Text('العملاء'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          leading: Builder(
            builder: (BuildContext context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.white),
              onPressed: () => Scaffold.of(context).openDrawer(),
            ),
          ),
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.contact_phone),
              onPressed: _importFromContacts,
              tooltip: 'استيراد من جهات الاتصال',
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.sort),
              onSelected: _handleSortAction,
              itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                const PopupMenuItem(
                  value: 'name',
                  child: Row(
                    children: <Widget>[
                      Icon(Icons.sort_by_alpha, size: 20),
                      SizedBox(width: 8),
                      Text('ترتيب حسب الاسم'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'balance',
                  child: Row(
                    children: <Widget>[
                      Icon(Icons.account_balance_wallet, size: 20),
                      SizedBox(width: 8),
                      Text('ترتيب حسب الرصيد'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'date',
                  child: Row(
                    children: <Widget>[
                      Icon(Icons.date_range, size: 20),
                      SizedBox(width: 8),
                      Text('ترتيب حسب التاريخ'),
                    ],
                  ),
                ),
              ],
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: _handleMenuAction,
              itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                const PopupMenuItem(
                  value: 'export',
                  child: ListTile(
                    leading: Icon(Icons.file_download),
                    title: Text('تصدير'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'import',
                  child: ListTile(
                    leading: Icon(Icons.file_upload),
                    title: Text('استيراد'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        body: Container(
          color: AppColors.background,
          child: Column(
            children: <Widget>[
              _buildSearchBar(),
              Expanded(child: _buildCustomerList()),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => context.go('/customers/add'),
          tooltip: 'إضافة عميل جديد',
          child: const Icon(Icons.person_add),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث عن عميل...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                )
              : null,
          filled: true,
          fillColor: AppColors.surfaceVariant,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            borderSide: BorderSide.none,
          ),
        ),
        onChanged: (String value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildCustomerList() {
    return Consumer<CustomerProvider>(
      builder: (BuildContext context, CustomerProvider customerProvider,
          Widget? child) {
        if (customerProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final List<Customer> filteredCustomers =
            _getFilteredCustomers(customerProvider.customers);

        if (filteredCustomers.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () async {
            await customerProvider.loadCustomers();
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            itemCount: filteredCustomers.length,
            itemBuilder: (BuildContext context, int index) {
              final Customer customer = filteredCustomers[index];
              return _buildCustomerCard(customer);
            },
          ),
        );
      },
    );
  }

  Widget _buildCustomerCard(Customer customer) {
    final double balance = customer.balance ?? 0;
    final bool hasDebt = balance < 0;
    final bool hasCredit = balance > 0;

    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: AppDimensions.elevationS,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: InkWell(
        onTap: () => context.go('/customers/details/${customer.id}'),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  CircleAvatar(
                    backgroundColor:
                        AppColors.primary.withAlpha((0.1 * 255).round()),
                    child: Text(
                      _getInitials(customer.name ?? ''),
                      style: AppStyles.titleSmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          customer.name ?? '',
                          style: AppStyles.titleMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (customer.phone?.isNotEmpty == true) ...<Widget>[
                          const SizedBox(height: AppDimensions.paddingXS),
                          Row(
                            children: <Widget>[
                              const Icon(
                                Icons.phone,
                                size: 14,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                Formatters.formatPhoneNumber(customer.phone!),
                                style: AppStyles.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildBalanceBadge(balance),
                ],
              ),
              if (customer.address?.isNotEmpty == true) ...<Widget>[
                const SizedBox(height: AppDimensions.paddingS),
                Row(
                  children: <Widget>[
                    const Icon(
                      Icons.location_on,
                      size: 14,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        customer.address!,
                        style: AppStyles.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      _buildInfoChip(
                        'المعاملات',
                        '0', // TODO: حساب عدد المعاملات
                        Icons.receipt,
                        AppColors.info,
                      ),
                      const SizedBox(width: AppDimensions.paddingS),
                      if (hasDebt)
                        _buildInfoChip(
                          'مديون',
                          Formatters.formatCurrency(balance.abs()),
                          Icons.trending_down,
                          AppColors.error,
                        )
                      else if (hasCredit)
                        _buildInfoChip(
                          'رصيد',
                          Formatters.formatCurrency(balance),
                          Icons.trending_up,
                          AppColors.success,
                        ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      IconButton(
                        icon: const Icon(Icons.visibility, size: 18),
                        onPressed: () =>
                            context.go('/customers/details/${customer.id}'),
                        tooltip: 'عرض',
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit, size: 18),
                        onPressed: () =>
                            context.go('/customers/edit/${customer.id}'),
                        tooltip: 'تعديل',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, size: 18),
                        onPressed: () => _deleteCustomer(customer),
                        tooltip: 'حذف',
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBalanceBadge(double balance) {
    Color color;
    String text;
    IconData icon;

    if (balance < 0) {
      color = AppColors.error;
      text = 'مديون';
      icon = Icons.trending_down;
    } else if (balance > 0) {
      color = AppColors.success;
      text = 'رصيد';
      icon = Icons.trending_up;
    } else {
      color = AppColors.textSecondary;
      text = 'متوازن';
      icon = Icons.balance;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: AppStyles.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            '$label: $value',
            style: AppStyles.labelSmall.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.people_outline,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            'لا يوجد عملاء',
            style: AppStyles.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            'ابدأ بإضافة عملاء جدد',
            style: AppStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          ElevatedButton.icon(
            onPressed: () => context.go('/customers/add'),
            icon: const Icon(Icons.person_add),
            label: const Text('إضافة عميل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '؟';

    final List<String> words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    } else {
      return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'
          .toUpperCase();
    }
  }

  List<Customer> _getFilteredCustomers(List<Customer> customers) {
    List<Customer> filtered = customers.where((Customer customer) {
      if (_searchQuery.isNotEmpty) {
        final String query = _searchQuery.toLowerCase();
        final String name = (customer.name ?? '').toLowerCase();
        final String phone = (customer.phone ?? '').toLowerCase();
        final String address = (customer.address ?? '').toLowerCase();

        return name.contains(query) ||
            phone.contains(query) ||
            address.contains(query);
      }
      return true;
    }).toList();

    // ترتيب النتائج
    filtered.sort((Customer a, Customer b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'name':
          comparison = (a.name ?? '').compareTo(b.name ?? '');
          break;
        case 'balance':
          comparison = (a.balance ?? 0).compareTo(b.balance ?? 0);
          break;
        case 'date':
          comparison = (a.createdAt ?? DateTime.now())
              .compareTo(b.createdAt ?? DateTime.now());
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = '';
    });
  }

  void _handleSortAction(String sortBy) {
    setState(() {
      if (_sortBy == sortBy) {
        _sortAscending = !_sortAscending;
      } else {
        _sortBy = sortBy;
        _sortAscending = true;
      }
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportCustomers();
        break;
      case 'import':
        _importCustomers();
        break;
    }
  }

  void _exportCustomers() {
    // TODO: تنفيذ تصدير العملاء
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ التصدير قريباً');
  }

  void _importCustomers() {
    // TODO: تنفيذ استيراد العملاء من ملف
    SnackBarHelper.showInfo(context, 'سيتم تنفيذ الاستيراد من ملف قريباً');
  }

  /// استيراد العملاء من جهات الاتصال
  Future<void> _importFromContacts() async {
    try {
      await showDialog(
        context: context,
        builder: (BuildContext context) => ImportContactsDialog(
          type: 'customer',
          onContactsSelected: _handleContactsImport,
        ),
      );
    } catch (e) {
      SnackBarHelper.showError(context, 'حدث خطأ في فتح جهات الاتصال: $e');
    }
  }

  /// معالجة استيراد جهات الاتصال المحددة
  Future<void> _handleContactsImport(List<Contact> selectedContacts) async {
    if (selectedContacts.isEmpty) {
      SnackBarHelper.showInfo(context, 'لم يتم اختيار أي جهة اتصال');
      return;
    }

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // استيراد جهات الاتصال
      final CustomerProvider customerProvider =
          context.read<CustomerProvider>();
      final Map<String, dynamic> result =
          await customerProvider.importCustomersFromContacts(selectedContacts);

      // إغلاق مؤشر التحميل
      Navigator.pop(context);

      // عرض النتائج
      if (result['success'] == true) {
        final int imported = result['imported'] as int;
        final int skipped = result['skipped'] as int;
        final List<String> errors = result['errors'] as List<String>;

        String message = 'تم استيراد $imported عميل بنجاح';
        if (skipped > 0) {
          message +=
              '\nتم تخطي $skipped جهة اتصال (موجودة مسبقاً أو غير صالحة)';
        }

        if (errors.isNotEmpty && errors.length <= 3) {
          message += '\n\nأخطاء:\n${errors.join('\n')}';
        } else if (errors.length > 3) {
          message += '\n\nحدثت ${errors.length} أخطاء أثناء الاستيراد';
        }

        SnackBarHelper.showSuccess(context, message);
      } else {
        final List<String> errors = result['errors'] as List<String>;
        String errorMessage = 'فشل في استيراد جهات الاتصال';
        if (errors.isNotEmpty) {
          errorMessage += ':\n${errors.first}';
        }
        SnackBarHelper.showError(context, errorMessage);
      }
    } catch (e) {
      // إغلاق مؤشر التحميل في حالة الخطأ
      Navigator.pop(context);
      SnackBarHelper.showError(context, 'حدث خطأ أثناء الاستيراد: $e');
    }
  }

  void _deleteCustomer(Customer customer) async {
    final bool? confirmed = await EnhancedConfirmationDialog.showDelete(
      context,
      itemName: customer.name ?? 'العميل',
    );

    if (confirmed == true) {
      try {
        await context.read<CustomerProvider>().deleteCustomer(customer.id!);
        SnackBarHelper.showSuccess(context, 'تم حذف العميل بنجاح');
      } catch (e) {
        SnackBarHelper.showError(context, 'فشل في حذف العميل: $e');
      }
    }
  }
}
