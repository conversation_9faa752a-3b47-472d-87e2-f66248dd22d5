import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:inventory_management_app/models/product.dart';
import 'package:inventory_management_app/models/category.dart';
import 'package:inventory_management_app/models/customer.dart';
import 'package:inventory_management_app/services/product_service.dart';
import 'package:inventory_management_app/services/category_service.dart';
import 'package:inventory_management_app/services/customer_service.dart';

void main() {
  // Initialize FFI
  sqfliteFfiInit();

  group('Database Tests', () {
    late ProductService productService;
    late CategoryService categoryService;
    late CustomerService customerService;

    setUpAll(() {
      // Use the ffi factory for testing
      databaseFactory = databaseFactoryFfi;
      productService = ProductService();
      categoryService = CategoryService();
      customerService = CustomerService();
    });

    group('Product Tests', () {
      test('should insert and retrieve a product', () async {
        // Create a test product
        final Product product = Product(
          name: 'Test Product',
          description: 'This is a test product',
          price: 99.99,
          quantity: 10,
        );

        // Insert the product
        final int id = await productService.insertProduct(product);
        expect(id, isPositive);

        // Retrieve the product
        final Product? retrievedProduct =
            await productService.getProductById(id);
        expect(retrievedProduct, isNotNull);
        expect(retrievedProduct!.name, equals('Test Product'));
        expect(retrievedProduct.price, equals(99.99));
        expect(retrievedProduct.quantity, equals(10.0));
      });

      test('should update a product', () async {
        // Create and insert a product
        final Product product = Product(
          name: 'Original Product',
          description: 'Original description',
          price: 50.0,
          quantity: 5,
        );

        final int id = await productService.insertProduct(product);

        // Create updated product using copyWith
        final Product updatedProduct = product.copyWith(
          id: id,
          name: 'Updated Product',
          price: 75.0,
        );

        final int updateResult =
            await productService.updateProduct(updatedProduct);
        expect(updateResult, equals(1));

        // Verify the update
        final Product? retrievedProduct =
            await productService.getProductById(id);
        expect(retrievedProduct?.name, equals('Updated Product'));
        expect(retrievedProduct?.price, equals(75.0));
      });

      test('should delete a product', () async {
        // Create and insert a product
        final Product product = Product(
          name: 'Product to Delete',
          description: 'This will be deleted',
          price: 25.0,
          quantity: 3,
        );

        final int id = await productService.insertProduct(product);

        // Delete the product
        final int deleteResult = await productService.deleteProduct(id);
        expect(deleteResult, equals(1));

        // Verify deletion
        final Product? deletedProduct = await productService.getProductById(id);
        expect(deletedProduct, isNull);
      });

      test('should search products by name', () async {
        // Insert test products
        await productService.insertProduct(Product(
          name: 'Apple iPhone',
          description: 'Smartphone',
          price: 999.0,
          quantity: 5,
        ));

        await productService.insertProduct(Product(
          name: 'Apple MacBook',
          description: 'Laptop',
          price: 1999.0,
          quantity: 2,
        ));

        await productService.insertProduct(Product(
          name: 'Samsung Galaxy',
          description: 'Smartphone',
          price: 799.0,
          quantity: 8,
        ));

        // Search for Apple products
        final List<Product> appleProducts =
            await productService.searchProductsByName('Apple');
        expect(appleProducts.length, equals(2));
        expect(appleProducts.every((Product p) => p.name.contains('Apple')),
            isTrue);
      });
    });

    group('Category Tests', () {
      test('should insert and retrieve a category', () async {
        final Category category = Category(
          name: 'Electronics',
          description: 'Electronic devices and gadgets',
        );

        final int id = await categoryService.insertCategory(category);
        expect(id, isPositive);

        final Category? retrievedCategory =
            await categoryService.getCategoryById(id);
        expect(retrievedCategory, isNotNull);
        expect(retrievedCategory!.name, equals('Electronics'));
        expect(retrievedCategory.description,
            equals('Electronic devices and gadgets'));
      });
    });

    group('Customer Tests', () {
      test('should insert and retrieve a customer', () async {
        final Customer customer = Customer(
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: '123 Main St, City, Country',
        );

        final int id = await customerService.insertCustomer(customer);
        expect(id, isPositive);

        final Customer? retrievedCustomer =
            await customerService.getCustomerById(id);
        expect(retrievedCustomer, isNotNull);
        expect(retrievedCustomer!.name, equals('John Doe'));
        expect(retrievedCustomer.email, equals('<EMAIL>'));
      });
    });
  });
}
